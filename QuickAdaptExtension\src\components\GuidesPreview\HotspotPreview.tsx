import React, { useEffect, useState, useRef } from "react";
import { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from "@mui/material";
import { GuideData } from "../drawer/Drawer";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore, { DrawerState } from "../../store/drawerStore";
// import { GetGudeDetailsByGuideId } from "../../services/GuideListServices";
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';



interface ButtonAction {
  Action: string;
  ActionValue: string;
  TargetUrl: string;
}
interface PopupProps {
    isHotspotPopupOpen: any;
    showHotspotenduser: any;
    anchorEl: null | HTMLElement;
    guideStep: any[];
    title: string;
    text: string;
    imageUrl?: string;
    videoUrl?: string;
    previousButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    continueButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    onClose: () => void;
    onPrevious: () => void;
    onContinue: () => void;
    currentStep: number;
    totalSteps: number;
    onDontShowAgain: () => void;
    progress: number;
    textFieldProperties?: any;
    imageProperties?: any;
    customButton?: any;
    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };
    canvasProperties?: {
        Position?: string;
        Padding?: string;
        Radius?: string;
        BorderSize?: string;
        BorderColor?: string;
        BackgroundColor?: string;
        Width?: string;
    };
    htmlSnippet: string;
    OverlayValue: boolean;
    savedGuideData: GuideData | null;
    hotspotProperties: any;
    handleHotspotHover: () => any;
    handleHotspotClick: () => any;
}

interface ButtonProperties {
  Padding: number;
  Width: number;
  Font: number;
  FontSize: number;
  ButtonTextColor: string;
  ButtonBackgroundColor: string;
}

interface ButtonData {
  ButtonStyle: string;
  ButtonName: string;
  Alignment: string;
  BackgroundColor: string;
  ButtonAction: ButtonAction;
  Padding: {
    Top: number;
    Right: number;
    Bottom: number;
    Left: number;
  };
  ButtonProperties: ButtonProperties;
}

interface HotspotProperties {
  size: string;
  type: string;
  color: string;
  showUpon: string;
  showByDefault: boolean;
  stopAnimation: boolean;
  pulseAnimation: boolean;
  position: {
    XOffset: string;
    YOffset: string;
  };
}

interface Step {
  xpath: string;
  hotspotProperties: HotspotProperties;
  content: string | JSX.Element;
  targetUrl: string;
  imageUrl: string;
  buttonData: ButtonData[];
}

interface HotspotGuideProps {
  steps: Step[];
  currentUrl: string;
  onClose: () => void;
}

const HotspotPreview: React.FC<PopupProps> = ({
    anchorEl,
    guideStep,
    title,
    text,
    imageUrl,
    onClose,
    onPrevious,
    onContinue,
    videoUrl,
    currentStep,
    totalSteps,
    onDontShowAgain,
    progress,
    textFieldProperties,
    imageProperties,
    customButton,
    modalProperties,
    canvasProperties,
    htmlSnippet,
    previousButtonStyles,
    continueButtonStyles,
    OverlayValue,
    savedGuideData,
    hotspotProperties,
    handleHotspotHover,
    handleHotspotClick,
    isHotspotPopupOpen,
   showHotspotenduser

}) => {
	const {
		setCurrentStep,
		selectedTemplate,
		toolTipGuideMetaData,
		elementSelected,
		axisData,
		tooltipXaxis,
		tooltipYaxis,
		setOpenTooltip,
		openTooltip,
		pulseAnimationsH,
		hotspotGuideMetaData,
		selectedTemplateTour,
		selectedOption,
		ProgressColor,
	} = useDrawerStore((state: DrawerState) => state);
	const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
	// State to track if the popover should be shown
	// State for popup visibility is managed through openTooltip
	const [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);
	const [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning
	const contentRef = useRef<HTMLDivElement>(null);
	const buttonContainerRef = useRef<HTMLDivElement>(null);
	let hotspot: any;
	const getElementByXPath = (xpath: string): HTMLElement | null => {
		const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
		const node = result.singleNodeValue;
		if (node instanceof HTMLElement) {
			return node;
		} else if (node?.parentElement) {
			return node.parentElement; // Return parent if it's a text node
		} else {
			return null;
		}
	};
	let xpath: any;
	if (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;
	const getElementPosition = (xpath: string | undefined) => {
		const element = getElementByXPath(xpath || "");
		if (element) {
			const rect = element.getBoundingClientRect();
			return {
				top: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll
				left: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll
			};
		}
		return null;
	};
	  // State to track if scrolling is needed
	  const [needsScrolling, setNeedsScrolling] = useState(false);
	  const scrollbarRef = useRef<any>(null);
	// Function to get estimated popup dimensions
	const getEstimatedPopupDimensions = () => {
		// Try to get actual dimensions from content if available
		if (contentRef.current) {
			const contentRect = contentRef.current.getBoundingClientRect();
			return {
				width: Math.max(contentRect.width, 50), // Minimum width
				height: Math.max(contentRect.height + 100, 150) // Add padding for buttons/progress
			};
		}

		// Use the new width calculation logic for more accurate estimation
		const widthStyling = getWidthStyling();
		let estimatedWidth = 300; // Default fallback

		if (widthStyling.width === 'auto') {
			// For auto-width, estimate based on content but respect maxWidth
			const maxWidth = parseInt(widthStyling.maxWidth) || 300;
			// Estimate content width (this could be improved with actual content measurement)
			estimatedWidth = Math.min(maxWidth, 250); // Conservative estimate for auto-sizing
		} else {
			// For fixed width, use the specified width
			estimatedWidth = parseInt(widthStyling.width) || 300;
		}

		const estimatedHeight = 250; // Standard height for all content types

		return {
			width: estimatedWidth,
			height: estimatedHeight
		};
	};

	// Function to calculate smart popup position that stays within viewport
	const calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {
		const hotspotLeft = elementRect.x + xOffset;
		const hotspotTop = elementRect.y + yOffset;

		// Get viewport dimensions
		const viewportWidth = window.innerWidth;
		const viewportHeight = window.innerHeight;

		// Get estimated popup dimensions
		const { width: popupWidth, height: popupHeight } = getEstimatedPopupDimensions();

		// Viewport margin to ensure tooltip doesn't touch edges
		const VIEWPORT_MARGIN = 20;

		// Gap between hotspot and tooltip
		const TOOLTIP_GAP = 10;

		// Handle very small viewports - reduce margins if popup is too large
		const availableWidth = viewportWidth - (VIEWPORT_MARGIN * 2);
		const availableHeight = viewportHeight - (VIEWPORT_MARGIN * 2);
		const effectiveMargin = Math.min(VIEWPORT_MARGIN,
			Math.max(5, Math.min((availableWidth - popupWidth) / 2, (availableHeight - popupHeight) / 2))
		);

		// Calculate hotspot bounds
		const hotspotRight = hotspotLeft + hotspotSize;
		const hotspotBottom = hotspotTop + hotspotSize;

		// Define possible positions in order of preference
		const positions = [
			// Default: bottom-right
			{
				name: 'bottom-right',
				left: hotspotRight + TOOLTIP_GAP,
				top: hotspotBottom + TOOLTIP_GAP
			},
			// Alternative: bottom-left
			{
				name: 'bottom-left',
				left: hotspotLeft - popupWidth - TOOLTIP_GAP,
				top: hotspotBottom + TOOLTIP_GAP
			},
			// Alternative: top-right
			{
				name: 'top-right',
				left: hotspotRight + TOOLTIP_GAP,
				top: hotspotTop - popupHeight - TOOLTIP_GAP
			},
			// Alternative: top-left
			{
				name: 'top-left',
				left: hotspotLeft - popupWidth - TOOLTIP_GAP,
				top: hotspotTop - popupHeight - TOOLTIP_GAP
			},
			// Alternative: right-center
			{
				name: 'right-center',
				left: hotspotRight + TOOLTIP_GAP,
				top: hotspotTop + (hotspotSize / 2) - (popupHeight / 2)
			},
			// Alternative: left-center
			{
				name: 'left-center',
				left: hotspotLeft - popupWidth - TOOLTIP_GAP,
				top: hotspotTop + (hotspotSize / 2) - (popupHeight / 2)
			}
		];

		// Function to check if a position fits within viewport
		const isPositionValid = (pos: { left: number; top: number }) => {
			const right = pos.left + popupWidth;
			const bottom = pos.top + popupHeight;

			return (
				pos.left >= effectiveMargin &&
				pos.top >= effectiveMargin &&
				right <= viewportWidth - effectiveMargin &&
				bottom <= viewportHeight - effectiveMargin
			);
		};

		// Find the first valid position
		let selectedPosition = positions.find(pos => isPositionValid(pos));

		// If no position fits perfectly, use the default and adjust to fit
		if (!selectedPosition) {
			selectedPosition = positions[0]; // Default: bottom-right

			// Adjust horizontally if needed
			if (selectedPosition.left + popupWidth > viewportWidth - effectiveMargin) {
				selectedPosition.left = viewportWidth - popupWidth - effectiveMargin;
			}
			if (selectedPosition.left < effectiveMargin) {
				selectedPosition.left = effectiveMargin;
			}

			// Adjust vertically if needed
			if (selectedPosition.top + popupHeight > viewportHeight - effectiveMargin) {
				selectedPosition.top = viewportHeight - popupHeight - effectiveMargin;
			}
			if (selectedPosition.top < effectiveMargin) {
				selectedPosition.top = effectiveMargin;
			}
		}

		// Debug logging (can be removed in production)
		if (process.env.NODE_ENV === 'development') {
			console.log('Smart positioning:', {
				hotspotPosition: { left: hotspotLeft, top: hotspotTop },
				hotspotSize: hotspotSize,
				popupDimensions: { width: popupWidth, height: popupHeight },
				viewport: { width: viewportWidth, height: viewportHeight },
				selectedPosition: selectedPosition.name,
				finalPosition: { left: selectedPosition.left, top: selectedPosition.top }
			});
		}

		return {
			top: selectedPosition.top + window.scrollY,
			left: selectedPosition.left + window.scrollX
		};
	};
	useEffect(() => {
		const element = getElementByXPath(xpath);
		if (element) {
			const rect = element.getBoundingClientRect();
			setPopupPosition({
				top: rect.top + window.scrollY, // Account for scrolling
				left: rect.left + window.scrollX,
			});
		}
	}, [xpath]);
	useEffect(() => {
		if (typeof window !== undefined) {
			const position = getElementPosition(xpath || "");
			if (position) {
				setPopupPosition(position);
			}
		}
	}, [xpath]);
	useEffect(() => {
		const element = getElementByXPath(xpath);
		// setTargetElement(element);
		if (element) {
		}
	}, [savedGuideData]);

	useEffect(() => {
		const element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);
		setTargetElement(element);
		if (element) {
			element.style.backgroundColor = "red !important";

			// Update popup position when target element changes
			const rect = element.getBoundingClientRect();
			setPopupPosition({
				top: rect.top + window.scrollY,
				left: rect.left + window.scrollX,
			});
		}
	}, [guideStep, currentStep]);

	// Hotspot styles are applied directly in the applyHotspotStyles function
	// State for overlay value
	const [, setOverlayValue] = useState(false);
	const handleContinue = () => {
		if (selectedTemplate !== "Tour") {
			if (currentStep < totalSteps) {
				setCurrentStep(currentStep + 1);
				onContinue();
				renderNextPopup(currentStep < totalSteps);
			}
		} else {
			setCurrentStep(currentStep + 1);
			const existingHotspot = document.getElementById("hotspotBlink");
			if (existingHotspot) {
				existingHotspot.style.display = "none";
				existingHotspot.remove();
			}
		}
	};

	const renderNextPopup = (shouldRenderNextPopup: boolean) => {
		return shouldRenderNextPopup ? (
			<HotspotPreview
				isHotspotPopupOpen={isHotspotPopupOpen}
				showHotspotenduser={showHotspotenduser}
				handleHotspotHover={handleHotspotHover}
				handleHotspotClick={handleHotspotClick}
				anchorEl={anchorEl}
				savedGuideData={savedGuideData}
				guideStep={guideStep}
				onClose={onClose}
				onPrevious={handlePrevious}
				onContinue={handleContinue}
				title={title}
				text={text}
				imageUrl={imageUrl}
				currentStep={currentStep + 1}
				totalSteps={totalSteps}
				onDontShowAgain={onDontShowAgain}
				progress={progress}
				textFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}
				imageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}
				customButton={
					savedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>
						section.CustomButtons.map((button: any) => ({
							...button,
							ContainerId: section.Id, // Attach the container ID for grouping
						}))
					)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []
				}
				modalProperties={modalProperties}
				canvasProperties={canvasProperties}
				htmlSnippet={htmlSnippet}
				OverlayValue={OverlayValue}
				hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}
			/>
		) : null;
	};

	const handlePrevious = () => {
		if (currentStep > 1) {
			setCurrentStep(currentStep - 1);
			onPrevious();
		}
	};
	useEffect(() => {
		if (OverlayValue) {
			setOverlayValue(true);
		} else {
			setOverlayValue(false);
		}
	}, [OverlayValue]);
	// Image fit is used directly in the component
	const getAnchorAndTransformOrigins = (
		position: string
	): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {
		switch (position) {
			case "top-left":
				return {
					anchorOrigin: { vertical: "top", horizontal: "left" },
					transformOrigin: { vertical: "bottom", horizontal: "right" },
				};
			case "top-right":
				return {
					anchorOrigin: { vertical: "top", horizontal: "right" },
					transformOrigin: { vertical: "bottom", horizontal: "left" },
				};
			case "bottom-left":
				return {
					anchorOrigin: { vertical: "bottom", horizontal: "left" },
					transformOrigin: { vertical: "top", horizontal: "right" },
				};
			case "bottom-right":
				return {
					anchorOrigin: { vertical: "bottom", horizontal: "right" },
					transformOrigin: { vertical: "center", horizontal: "left" },
				};
			case "center-center":
				return {
					anchorOrigin: { vertical: "center", horizontal: "center" },
					transformOrigin: { vertical: "center", horizontal: "center" },
				};
			case "top-center":
				return {
					anchorOrigin: { vertical: "top", horizontal: "center" },
					transformOrigin: { vertical: "bottom", horizontal: "center" },
				};
			case "left-center":
				return {
					anchorOrigin: { vertical: "center", horizontal: "left" },
					transformOrigin: { vertical: "center", horizontal: "right" },
				};
			case "bottom-center":
				return {
					anchorOrigin: { vertical: "bottom", horizontal: "center" },
					transformOrigin: { vertical: "center", horizontal: "center" },
				};
			case "right-center":
				return {
					anchorOrigin: { vertical: "center", horizontal: "right" },
					transformOrigin: { vertical: "center", horizontal: "left" },
				};
			default:
				return {
					anchorOrigin: { vertical: "center", horizontal: "center" },
					transformOrigin: { vertical: "center", horizontal: "center" },
				};
		}
	};

	const { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || "center center");

	const textStyle = {
		fontWeight: textFieldProperties?.TextProperties?.Bold ? "bold" : "normal",
		fontStyle: textFieldProperties?.TextProperties?.Italic ? "italic" : "normal",
		color: textFieldProperties?.TextProperties?.TextColor || "#000000",
		textAlign: textFieldProperties?.Alignment || "left",
	};

	// Image styles are applied directly in the component

	const renderHtmlSnippet = (snippet: string) => {
		// Return the raw HTML snippet for rendering
		return {
			__html: snippet.replace(/(<a\s+[^>]*href=")([^"]*)("[^>]*>)/g, (_match, p1, p2, p3) => {
				return `${p1}${p2}" target="_blank"${p3}`;
			}),
		};
	};



	// Function to check if canvas width has been modified from default
	const isCanvasWidthModified = () => {
		const defaultWidth = "300"; // Default width without "px"
		const currentWidth = canvasProperties?.Width?.replace("px", "") || defaultWidth;
		return currentWidth !== defaultWidth;
	};

	// Function to get width styling based on user preferences
	const getWidthStyling = () => {
		// Check if user has modified canvas width from default
		const hasCustomWidth = isCanvasWidthModified();

		if (hasCustomWidth && canvasProperties?.Width) {
			// User-specified width: use exact width, override auto-sizing and max-width
			const userWidth = canvasProperties.Width.includes('px')
				? canvasProperties.Width
				: `${canvasProperties.Width}px`;
			return {
				width: userWidth,
				maxWidth: userWidth, // Override the 300px limit
				minWidth: 'unset' // Remove any minimum width constraints
			};
		} else {
			// Default behavior: auto-adjust with 300px maximum, no minimum width
			return {
				width: 'auto', // Allow auto-sizing based on content
				maxWidth: '300px', // Maximum width constraint
				minWidth: 'unset' // Remove minimum width constraints for small content
			};
		}
	};

	// Update popup position when content changes (width is now calculated dynamically)
	useEffect(() => {
		// Recalculate popup position when content changes since width affects positioning
		if (xpath && hotspotSize) {
			const element = getElementByXPath(xpath);
			if (element) {
				const rect = element.getBoundingClientRect();
				const hotspotPropData = toolTipGuideMetaData[0]?.hotspots;
				const xOffset = parseFloat(hotspotPropData?.XPosition || "4");
				const yOffset = parseFloat(hotspotPropData?.YPosition || "4");

				const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);
				setPopupPosition(popupPos);
			}
		}
	}, [textFieldProperties, imageProperties, customButton, currentStep, xpath, hotspotSize, toolTipGuideMetaData]);

	// Recalculate popup position when hotspot size changes or content dimensions change
	useEffect(() => {
		if (xpath && hotspotSize) {
			const element = getElementByXPath(xpath);
			if (element) {
				const rect = element.getBoundingClientRect();
				const hotspotPropData = toolTipGuideMetaData[0]?.hotspots;
				const xOffset = parseFloat(hotspotPropData?.XPosition || "4");
				const yOffset = parseFloat(hotspotPropData?.YPosition || "4");

				const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);
				setPopupPosition(popupPos);
			}
		}
	}, [hotspotSize, xpath, toolTipGuideMetaData, textFieldProperties, imageProperties, customButton]);

	// Recalculate popup position on window resize
	useEffect(() => {
		const handleResize = () => {
			if (xpath && hotspotSize) {
				const element = getElementByXPath(xpath);
				if (element) {
					const rect = element.getBoundingClientRect();
					const hotspotPropData = toolTipGuideMetaData[0]?.hotspots;
					const xOffset = parseFloat(hotspotPropData?.XPosition || "4");
					const yOffset = parseFloat(hotspotPropData?.YPosition || "4");

					const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);
					setPopupPosition(popupPos);
				}
			}
		};

		window.addEventListener('resize', handleResize);
		return () => window.removeEventListener('resize', handleResize);
	}, [xpath, hotspotSize, toolTipGuideMetaData]);

	const groupedButtons = customButton.reduce((acc: any, button: any) => {
		const containerId = button.ContainerId || "default"; // Use a ContainerId or fallback
		if (!acc[containerId]) {
			acc[containerId] = [];
		}
		acc[containerId].push(button);
		return acc;
	}, {});

	const widthStyling = getWidthStyling();
	const canvasStyle = {
		position: canvasProperties?.Position || "center-center",
		borderRadius: canvasProperties?.Radius || "4px",
		borderWidth: canvasProperties?.BorderSize || "0px",
		borderColor: canvasProperties?.BorderColor || "black",
		borderStyle: "solid",
		backgroundColor: canvasProperties?.BackgroundColor || "white",
		...widthStyling, // Apply the width styling (width, maxWidth, minWidth)
	};
	const sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || "auto";
	const handleButtonAction = (action: any) => {
		if (action.Action === "open-url" || action.Action === "open" || action.Action === "openurl") {
			const targetUrl = action.TargetUrl;
			if (action.ActionValue === "same-tab") {
				// Open the URL in the same tab
				window.location.href = targetUrl;
			} else {
				// Open the URL in a new tab
				window.open(targetUrl, "_blank", "noopener noreferrer");
			}
		} else {
			if (
				action.Action == "Previous" ||
				action.Action == "previous" ||
				action.ActionValue == "Previous" ||
				action.ActionValue == "Previous"
			) {
				handlePrevious();
			} else if (
				action.Action == "Next" ||
				action.Action == "next" ||
				action.ActionValue == "Next" ||
				action.ActionValue == "next"
			) {
				handleContinue();
			} else if (
				action.Action == "Restart" ||
				action.ActionValue == "Restart"
			) {
				// Reset to the first step
				setCurrentStep(1);
				// If there's a specific URL for the first step, navigate to it
				if (savedGuideData?.GuideStep?.[0]?.ElementPath) {
					const firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);
					if (firstStepElement) {
						firstStepElement.scrollIntoView({ behavior: 'smooth' });
					}
				}
			}
		}
		setOverlayValue(false);
	};
	useEffect(() => {
		if (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {
			// Show tooltip by default
			setOpenTooltip(true);
		}
	}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);

	// Add effect to handle isHotspotPopupOpen prop changes
	useEffect(() => {
		if (isHotspotPopupOpen) {
			// Get the ShowUpon property
			const hotspotPropData = selectedTemplateTour === "Hotspot" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots
				? toolTipGuideMetaData[currentStep - 1].hotspots
				: toolTipGuideMetaData[0]?.hotspots;
			const hotspotData = selectedTemplateTour === "Hotspot"
				? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot
				: savedGuideData?.GuideStep?.[0]?.Hotspot;

			// Only show tooltip by default if ShowByDefault is true
			// For "Hovering Hotspot", we'll wait for the hover event
			if (hotspotPropData?.ShowByDefault) {
				// Set openTooltip to true
				setOpenTooltip(true);
			} else {
				// Otherwise, initially hide the tooltip
				setOpenTooltip(false);
			}
		}
	}, [isHotspotPopupOpen, toolTipGuideMetaData]);

	// Add effect to handle showHotspotenduser prop changes
	useEffect(() => {
		if (showHotspotenduser) {
			// Get the ShowUpon property
			const hotspotPropData = selectedTemplateTour === "Hotspot" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots
				? toolTipGuideMetaData[currentStep - 1].hotspots
				: toolTipGuideMetaData[0]?.hotspots;
			const hotspotData = selectedTemplateTour === "Hotspot"
				? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot
				: savedGuideData?.GuideStep?.[0]?.Hotspot;

			// Only show tooltip by default if ShowByDefault is true
			if (hotspotPropData?.ShowByDefault) {
				// Set openTooltip to true
				setOpenTooltip(true);
			} else {
				// Otherwise, initially hide the tooltip
				setOpenTooltip(false);
			}
		}
	}, [showHotspotenduser, toolTipGuideMetaData]);

	// Add a global click handler to detect clicks outside the hotspot to close the tooltip
	useEffect(() => {
		const handleGlobalClick = (e: MouseEvent) => {
			const hotspotElement = document.getElementById("hotspotBlink");

			// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)
			if (hotspotElement && hotspotElement.contains(e.target as Node)) {
				return;
			}

			// We want to keep the tooltip open once it's been displayed
			// So we're not closing it on clicks outside anymore
		};

		document.addEventListener("click", handleGlobalClick);

		return () => {
			document.removeEventListener("click", handleGlobalClick);
		};
	}, [toolTipGuideMetaData]);
	// Check if content needs scrolling with improved detection
	useEffect(() => {
		const checkScrollNeeded = () => {
			if (contentRef.current) {
				// Force a reflow to get accurate measurements
				contentRef.current.style.height = 'auto';
				const contentHeight = contentRef.current.scrollHeight;
				const containerHeight = 320; // max-height value
				const shouldScroll = contentHeight > containerHeight;


				setNeedsScrolling(shouldScroll);

				// Force update scrollbar
				if (scrollbarRef.current) {
					// Try multiple methods to update the scrollbar
					if (scrollbarRef.current.updateScroll) {
						scrollbarRef.current.updateScroll();
					}
					// Force re-initialization if needed
					setTimeout(() => {
						if (scrollbarRef.current && scrollbarRef.current.updateScroll) {
							scrollbarRef.current.updateScroll();
						}
					}, 10);
				}
			}
		};

		
		checkScrollNeeded();

		
		const timeouts = [
			setTimeout(checkScrollNeeded, 50),
			setTimeout(checkScrollNeeded, 100),
			setTimeout(checkScrollNeeded, 200),
			setTimeout(checkScrollNeeded, 500)
		];

		
		let resizeObserver: ResizeObserver | null = null;
		let mutationObserver: MutationObserver | null = null;

		if (contentRef.current && window.ResizeObserver) {
			resizeObserver = new ResizeObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			resizeObserver.observe(contentRef.current);
		}

		
		if (contentRef.current && window.MutationObserver) {
			mutationObserver = new MutationObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			mutationObserver.observe(contentRef.current, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ['style', 'class']
			});
		}

		return () => {
			timeouts.forEach(clearTimeout);
			if (resizeObserver) {
				resizeObserver.disconnect();
			}
			if (mutationObserver) {
				mutationObserver.disconnect();
			}
		};
	}, [currentStep]);
	// We no longer need the persistent monitoring effect since we want the tooltip
	// to close when the mouse leaves the hotspot

	function getAlignment(alignment: string) {
		switch (alignment) {
			case "start":
				return "flex-start";
			case "end":
				return "flex-end";
			case "center":
			default:
				return "center";
		}
	}
	const getCanvasPosition = (position: string = "center-center") => {
		switch (position) {
			case "bottom-left":
				return { top: "auto !important" };
			case "bottom-right":
				return { top: "auto !important" };
			case "bottom-center":
				return { top: "auto !important" };
			case "center-center":
				return { top: "25% !important" };
			case "left-center":
				return { top: imageUrl === "" ? "40% !important" : "20% !important" };
			case "right-center":
				return { top: "10% !important" };
			case "top-left":
				return { top: "10% !important" };
			case "top-right":
				return { top: "10% !important" };
			case "top-center":
				return { top: "9% !important" };
			default:
				return { top: "25% !important" };
		}
	};

		// function to get the correct property value based on tour vs normal hotspot
	const getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {
		if (selectedTemplateTour === "Hotspot") {
			// For tour hotspots, use saved data first, fallback to metadata
			switch (propName) {
				case 'PulseAnimation':
					return hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;
				case 'StopAnimation':
					// Always use stopAnimationUponInteraction for consistency
					return hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;
				case 'ShowUpon':
					return hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;
				case 'ShowByDefault':
					return hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;
				default:
					return hotspotPropData?.[propName];
			}
		} else {
			// For normal hotspots, use metadata
			if (propName === 'StopAnimation') {
				return hotspotPropData?.stopAnimationUponInteraction;
			}
			return hotspotPropData?.[propName];
		}
	};

	const applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {
		hotspot.style.position = "absolute";
		hotspot.style.left = `${left}px`;
		hotspot.style.top = `${top}px`;
		hotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided
		hotspot.style.height = `${hotspotPropData?.Size}px`;
		hotspot.style.backgroundColor = hotspotPropData?.Color;
		hotspot.style.borderRadius = "50%";
		hotspot.style.zIndex = "auto !important"; // Increased z-index
		hotspot.style.transition = "none";
		hotspot.style.pointerEvents = "auto"; // Ensure clicks are registered
		hotspot.innerHTML = "";

		if (hotspotPropData?.Type === "Info" || hotspotPropData?.Type === "Question") {
			const textSpan = document.createElement("span");
			textSpan.innerText = hotspotPropData.Type === "Info" ? "i" : "?";
			textSpan.style.color = "white";
			textSpan.style.fontSize = "14px";
			textSpan.style.fontWeight = "bold";
			textSpan.style.fontStyle = hotspotPropData.Type === "Info" ? "italic" : "normal";
			textSpan.style.display = "flex";
			textSpan.style.alignItems = "center";
			textSpan.style.justifyContent = "center";
			textSpan.style.width = "100%";
			textSpan.style.height = "100%";
			hotspot.appendChild(textSpan);
		}

		// Apply animation class if needed
		// Track if pulse has been stopped by hover
		const pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);
		const shouldPulse = selectedTemplateTour === "Hotspot"
			? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)
			: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);

		if (shouldPulse) {
            hotspot.classList.add("pulse-animation");
            hotspot.classList.remove("pulse-animation-removed");
        } else {
            hotspot.classList.remove("pulse-animation");
            hotspot.classList.add("pulse-animation-removed");
        }

		// Ensure the hotspot is visible and clickable
		hotspot.style.display = "flex";
		hotspot.style.pointerEvents = "auto";

		// No need for separate animation control functions here
		// Animation will be controlled directly in the event handlers
		// Set initial state of openTooltip based on ShowByDefault and ShowUpon
		const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);
		if (showByDefault) {
			setOpenTooltip(true);
		} else {
			// If not showing by default, only show based on interaction type
			//setOpenTooltip(false);
		}

		// Only clone and replace if the hotspot doesn't have event listeners already
		// This prevents losing the _pulseStopped state unnecessarily
		if (!hotspot.hasAttribute('data-listeners-attached')) {
			const newHotspot = hotspot.cloneNode(true) as HTMLElement;
			// Copy the _pulseStopped property if it exists
			if (hotspot._pulseStopped !== undefined) {
	            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;
	        }
			if (hotspot.parentNode) {
				hotspot.parentNode.replaceChild(newHotspot, hotspot);
				hotspot = newHotspot;
			}
		}

		// Ensure pointer events are enabled
		hotspot.style.pointerEvents = "auto";

		// Define combined event handlers that handle both animation and tooltip
		const showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);
		const handleHover = (e: Event) => {
			e.stopPropagation();
			console.log("Hover detected on hotspot");

			// Show tooltip if ShowUpon is "Hovering Hotspot"
			if (showUpon === "Hovering Hotspot") {
				// Set openTooltip to true when hovering
				setOpenTooltip(true);

				// Call the passed hover handler if it exists
				if (typeof handleHotspotHover === "function") {
					handleHotspotHover();
				}

				// Stop animation if configured to do so
				const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);
				if (stopAnimationSetting) {
					hotspot.classList.remove("pulse-animation");
					hotspot.classList.add("pulse-animation-removed");
					hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied
				}
			}
		};

		const handleMouseOut = (e: Event) => {
			e.stopPropagation();

			// Hide tooltip when mouse leaves the hotspot
			// Only if ShowUpon is "Hovering Hotspot" and not ShowByDefault
			const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);
			if (showUpon === "Hovering Hotspot" && !showByDefault) {
				// setOpenTooltip(false);
			}
		};

		const handleClick = (e: Event) => {
			e.stopPropagation();
			console.log("Click detected on hotspot");

			// Toggle tooltip if ShowUpon is "Clicking Hotspot" or not specified
			if (showUpon === "Clicking Hotspot" || !showUpon) {
				// Toggle the tooltip state
				setOpenTooltip(!openTooltip);

				// Call the passed click handler if it exists
				if (typeof handleHotspotClick === "function") {
					handleHotspotClick();
				}

				// Stop animation if configured to do so
const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);
				if (stopAnimationSetting) {
					hotspot.classList.remove("pulse-animation");
					hotspot.classList.add("pulse-animation-removed");
					hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied
				}
			}
		};

		// Add appropriate event listeners based on ShowUpon property
		if (!hotspot.hasAttribute('data-listeners-attached')) {
			if (showUpon === "Hovering Hotspot") {
				// For hover interaction
				hotspot.addEventListener("mouseover", handleHover);
				hotspot.addEventListener("mouseout", handleMouseOut);

				// Also add click handler for better user experience
				hotspot.addEventListener("click", handleClick);
			} else {
				// For click interaction (default)
				hotspot.addEventListener("click", handleClick);
			}

			// Mark that listeners have been attached
			hotspot.setAttribute('data-listeners-attached', 'true');
		}
	};
	useEffect(() => {
		let element;
		let steps;

		const fetchGuideDetails = async () => {
			try {
				//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);
				steps = savedGuideData?.GuideStep || [];

				// For tour hotspots, use the current step's element path
				const elementPath = selectedTemplateTour === "Hotspot" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath
					? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath
					: steps?.[0]?.ElementPath || "";

				element = getElementByXPath(elementPath || "");
				setTargetElement(element);

				if (element) {
					// element.style.outline = "2px solid red";
				}

				// Check if this is a hotspot scenario (normal or tour)
				const isHotspotScenario = selectedTemplate === "Hotspot" ||
					selectedTemplateTour === "Hotspot" ||
					title === "Hotspot" ||
					(selectedTemplate === "Tour" && selectedTemplateTour === "Hotspot");

				if (isHotspotScenario) {
					
					

					// Get hotspot properties - prioritize tour data for tour hotspots
					let hotspotPropData;
					let hotspotData;

					if (selectedTemplateTour === "Hotspot" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {
						// Tour hotspot - use current step metadata
						hotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;
						hotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;
					} else if (toolTipGuideMetaData?.[0]?.hotspots) {
						// Normal hotspot - use first metadata entry
						hotspotPropData = toolTipGuideMetaData[0].hotspots;
						hotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;
					} else {
						// Fallback to default values for tour hotspots without metadata
						hotspotPropData = {
							XPosition: "4",
							YPosition: "4",
							Type: "Question",
							Color: "yellow",
							Size: "16",
							PulseAnimation: true,
							stopAnimationUponInteraction: true,
							ShowUpon: "Hovering Hotspot",
							ShowByDefault: false,
						};
						hotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};
					}
					const xOffset = parseFloat(hotspotPropData?.XPosition || "4");
					const yOffset = parseFloat(hotspotPropData?.YPosition || "4");
					const currentHotspotSize = parseFloat(hotspotPropData?.Size || "30");

					// Update hotspot size state
					setHotspotSize(currentHotspotSize);

					let left, top;
					if (element) {
						const rect = element.getBoundingClientRect();
						left = rect.x + xOffset;
						top = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));

						// Calculate popup position below the hotspot
						const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);
						setPopupPosition(popupPos);
					}

					// Check if hotspot already exists, preserve it to maintain _pulseStopped state
					const existingHotspot = document.getElementById("hotspotBlink");
					if (existingHotspot) {
						hotspot = existingHotspot;
						// Don't reset _pulseStopped if it already exists
					} else {
						// Create new hotspot only if it doesn't exist
						hotspot = document.createElement("div");
						hotspot.id = "hotspotBlink"; // Fixed ID for easier reference
						hotspot._pulseStopped = false; // Set only on creation
						document.body.appendChild(hotspot);
					}

					hotspot.style.cursor = "pointer";
					hotspot.style.pointerEvents = "auto"; // Ensure it can receive mouse events

					// Make sure the hotspot is visible and clickable
					hotspot.style.zIndex = "9999";

					// If ShowByDefault is true, set openTooltip to true immediately
					if (hotspotPropData?.ShowByDefault) {
						setOpenTooltip(true);
					}

					// Set styles first
					applyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);

					// Set initial tooltip visibility based on ShowByDefault
					const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);
					if (showByDefault) {
						setOpenTooltip(true);
					} else {
						//setOpenTooltip(false);
					}

					// We don't need to add event listeners here as they're already added in applyHotspotStyles
				}
			} catch (error) {
				console.error("Error in fetchGuideDetails:", error);
			}
		};

		fetchGuideDetails();

		return () => {
			const existingHotspot = document.getElementById("hotspotBlink");
			if (existingHotspot) {
				existingHotspot.onclick = null;
				existingHotspot.onmouseover = null;
				existingHotspot.onmouseout = null;
			}
		};
	}, [
		savedGuideData,
		toolTipGuideMetaData,
		isHotspotPopupOpen,
		showHotspotenduser,
		selectedTemplateTour,
		currentStep,
		// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders
	]);
	const enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;

	function getProgressTemplate(selectedOption: any) {
		if (selectedOption === 1) {
			return "dots";
		} else if (selectedOption === 2) {
			return "linear";
		} else if (selectedOption === 3) {
			return "BreadCrumbs";
		} else if (selectedOption === 4) {
			return "breadcrumbs";
		}

		return savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || "dots";
	}
	const progressTemplate = getProgressTemplate(selectedOption);
	const renderProgress = () => {
		if (!enableProgress) return null;

		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={totalSteps}
					position="static"
					activeStep={currentStep - 1}
					sx={{
						backgroundColor: "transparent",
						position: "inherit !important",
						"& .MuiMobileStepper-dotActive": {
							backgroundColor: ProgressColor, // Active dot
						},
					}}
					backButton={<Button style={{ visibility: "hidden" }} />}
					nextButton={<Button style={{ visibility: "hidden" }} />}
				/>
			);
		}
		if (progressTemplate === "BreadCrumbs") {
			return (
				<Box sx={{ display: "flex", alignItems: "center", placeContent: "center", gap: "5px", padding: "8px" }}>
					{/* Custom Step Indicators */}

					{Array.from({ length: totalSteps }).map((_, index) => (
						<div
							key={index}
							style={{
								width: "14px",
								height: "4px",
								backgroundColor: index === currentStep - 1 ? ProgressColor : "#e0e0e0", // Active color and inactive color
								borderRadius: "100px",
							}}
						/>
					))}
				</Box>
			);
		}
		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{ display: "flex", alignItems: "center", placeContent: "flex-start" }}>
					<Typography sx={{ padding: "8px", color: ProgressColor }}>
						Step {currentStep} of {totalSteps}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box>
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={progress}
							sx={{
								height: "6px",
										borderRadius: "20px",
										margin: "6px 10px",
								"& .MuiLinearProgress-bar": {
									backgroundColor: ProgressColor, // progress bar color
								},
							}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
	};
	return (
		<>
			{targetElement && (
				<div>
					{/* {overlay && (
						<div
							style={{
								position: "fixed",
								top: 0,
								left: 0,
								right: 0,
								bottom: 0,
								backgroundColor: "rgba(0, 0, 0, 0.5)",
								zIndex: 999,
							}}
						/>
					)} */}
					{openTooltip && (
						<Popover
							open={Boolean(popupPosition) || Boolean(anchorEl)}
							anchorEl={anchorEl}
							onClose={() => {
								// We want to keep the tooltip open once it's been displayed
								// So we're not closing it on Popover close events
							}}
							anchorOrigin={anchorOrigin}
							transformOrigin={transformOrigin}
							anchorReference="anchorPosition"
							anchorPosition={
								popupPosition
									? {
											top: popupPosition.top +10+ (parseFloat(tooltipYaxis || "0") > 0 ? -parseFloat(tooltipYaxis || "0") : Math.abs(parseFloat(tooltipYaxis || "0"))),
											left: popupPosition.left +10+ parseFloat(tooltipXaxis || "0"),
									  }
									: undefined
							}
							sx={{
								// "& .MuiBackdrop-root": {
								//     position: 'relative !important', // Ensures higher specificity
								// },
								"pointer-events": anchorEl ? "auto" : "auto",
								'& .MuiPaper-root:not(.MuiMobileStepper-root)': {
									zIndex: 1000,
									// borderRadius: "1px",
									...canvasStyle,
									//...getAnchorAndTransformOrigins,
									//top: "16% !important",
									// top: canvasProperties?.Position === "bottom-left" ? "auto !important" :
									//     canvasProperties?.Position === "bottom-right" ? "auto !important" :
									//         canvasProperties?.Position === "bottom-center" ? "auto !important" :
									//             canvasProperties?.Position === "center-center" ? "30% !important" :
									//                 canvasProperties?.Position === "left-center" ? (imageUrl === "" ? "40% !important" : "20% !important") :
									//                     canvasProperties?.Position === "right-center" ? "20% !important" :
									//                         canvasProperties?.Position === "top-left" ? "10% !important" :
									//                         canvasProperties?.Position === "top-center" ? "9% !important" :
									//                         canvasProperties?.Position==="top-right"?"10% !important":    "",
									...getCanvasPosition(canvasProperties?.Position || "center-center"),
									top: `${(popupPosition?.top || 0)
										+ (tooltipYaxis && tooltipYaxis != 'undefined'
											? parseFloat(tooltipYaxis || "0") > 0
												? -parseFloat(tooltipYaxis || "0")
												: Math.abs(parseFloat(tooltipYaxis || "0"))
											: 0)}px !important`,
									left: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'
										? (parseFloat(tooltipXaxis) || 0)
										: 0 )}px !important`,
									overflow: "hidden",
									// Add smooth transitions for position changes
									transition: 'top 0.3s ease-out, left 0.3s ease-out',
								},
							}}
							disableScrollLock={true}
						>
							<div style={{ placeContent: "end", display: "flex" }}>
								{modalProperties?.DismissOption && (
									<IconButton
										onClick={() => {
											// Only close if explicitly requested by user clicking the close button
											//setOpenTooltip(false);
										}}
										sx={{
											position: "fixed",
											boxShadow: "rgba(0, 0, 0, 0.06) 0px 4px 8px",
											left: "auto",
											right: "auto",
											margin: "-15px",
											background: "#fff !important",
											border: "1px solid #ccc",
											zIndex: "999999",
											borderRadius: "50px",
											padding: "5px !important",
										}}
									>
										<CloseIcon sx={{ zoom: 1, color: "#000" }} />
									</IconButton>
								)}
							</div>
							<PerfectScrollbar
				key={`scrollbar-${needsScrolling}`}
				ref={scrollbarRef}
				style={{ maxHeight: "400px" }}
				options={{
					suppressScrollY: !needsScrolling,
					suppressScrollX: true,
					wheelPropagation: false,
					swipeEasing: true,
					minScrollbarLength: 20,
					scrollingThreshold: 1000,
					scrollYMarginOffset: 0
				}}
			>
							<div style={{
								maxHeight: "400px",
								overflow: "hidden auto"
							}}>
								<Box style={{
									padding: canvasProperties?.Padding || "10px",
									height: sectionHeight
								}}>
									<Box
										ref={contentRef}
										display="flex"
										flexDirection="column"
										flexWrap="wrap"
										justifyContent="center"
										sx={{
											width: "100%",
											padding: "8px",
											boxSizing: "border-box"
										}}
									>
										{imageProperties?.map((imageProp: any) =>
											imageProp.CustomImage.map((customImg: any, imgIndex: number) => (
												<Box
													key={`${imageProp.Id}-${imgIndex}`}
													component="img"
													src={customImg.Url}
													alt={customImg.AltText || "Image"}
													sx={{
														maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || "500px",
														textAlign: imageProp.Alignment || "center",
														objectFit: customImg.Fit || "contain",
														//  width: "500px",
														height: `${customImg.SectionHeight || 250}px`,
														background: customImg.BackgroundColor || "#ffffff",
														margin: "10px 0",
													}}
													onClick={() => {
														if (imageProp.Hyperlink) {
															const targetUrl = imageProp.Hyperlink;
															window.open(targetUrl, "_blank", "noopener noreferrer");
														}
													}}
													style={{ cursor: imageProp.Hyperlink ? "pointer" : "default" }}
												/>
											))
										)}

										{textFieldProperties?.map(
											(textField: any, index: any) =>
												textField.Text && (
													<Typography
														className="qadpt-preview"
														key={textField.Id || index} // Use a unique key, either Id or index
														sx={{
															textAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,
															color: textField.TextProperties?.TextColor || textStyle.color,
															whiteSpace: "pre-wrap",
															wordBreak: "break-word",
															padding: "0 5px",
															wordWrap: "break-word",
															overflowWrap: "break-word",
															hyphens: "auto",
														}}
														dangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML
													/>
												)
										)}
									</Box>

									{Object.keys(groupedButtons).map((containerId) => (
										<Box
											ref={buttonContainerRef}
											key={containerId}
											sx={{
												display: "flex",
												justifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),
												flexWrap: "wrap",
												margin: "5px 0",
												backgroundColor: groupedButtons[containerId][0]?.BackgroundColor,
												padding: "5px 0",
												width: "100%"
											}}
										>
											{groupedButtons[containerId].map((button: any, index: number) => (
												<Button
													key={index}
													onClick={() => handleButtonAction(button.ButtonAction)}
													variant="contained"
													sx={{
														margin: "0 5px 5px 5px",
														backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#007bff",
														color: button.ButtonProperties?.ButtonTextColor || "#fff",
														border: button.ButtonProperties?.ButtonBorderColor || "transparent",
														fontSize: button.ButtonProperties?.FontSize || "15px",
														width: button.ButtonProperties?.Width || "auto",
														padding: "4px 8px",
														lineHeight: "normal",
														textTransform: "none",
														borderRadius: button.ButtonProperties?.BorderRadius || "8px",
														boxShadow: "none !important", // Remove box shadow in normal state
														"&:hover": {
															backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#007bff", // Keep the same background color on hover
															opacity: 0.9, // Slightly reduce opacity on hover for visual feedback
															boxShadow: "none !important", // Remove box shadow in hover state
														},
													}}
												>
													{button.ButtonName}
												</Button>
											))}
										</Box>
									))}
								</Box>

								
								</div>
								</PerfectScrollbar>
							
								{enableProgress && totalSteps>1 && selectedTemplate === "Tour" && <Box>{renderProgress()}</Box>}{" "}
						</Popover>
					)}
				
				</div>
			)}

			<style>
				{`
          @keyframes pulse {
            0% {
              transform: scale(1);
              opacity: 1;
            }
            50% {
              transform: scale(1.5);
              opacity: 0.6;
            }
            100% {
              transform: scale(1);
              opacity: 1;
            }
          }

          .pulse-animation {
            animation: pulse 1.5s infinite;
            pointer-events: auto !important;
          }

          .pulse-animation-removed {
            pointer-events: auto !important;
          }
        `}
			</style>
			
		</>
	);
};

export default HotspotPreview;
