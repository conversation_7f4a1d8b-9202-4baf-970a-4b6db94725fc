{"ast": null, "code": "import React,{useEffect,useState,useRef}from\"react\";import{Box,Button,IconButton,LinearProgress,MobileStepper,Popover,Typography}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const HotspotPreview=_ref=>{var _savedGuideData$Guide,_savedGuideData$Guide2,_textFieldProperties$,_textFieldProperties$2,_textFieldProperties$3,_imageProperties,_imageProperties$Cust,_imageProperties$Cust2,_savedGuideData$Guide31,_savedGuideData$Guide32,_savedGuideData$Guide33;let{anchorEl,guideStep,title,text,imageUrl,onClose,onPrevious,onContinue,videoUrl,currentStep,totalSteps,onDontShowAgain,progress,textFieldProperties,imageProperties,customButton,modalProperties,canvasProperties,htmlSnippet,previousButtonStyles,continueButtonStyles,OverlayValue,savedGuideData,hotspotProperties,handleHotspotHover,handleHotspotClick,isHotspotPopupOpen,showHotspotenduser}=_ref;const{setCurrentStep,selectedTemplate,toolTipGuideMetaData,elementSelected,axisData,tooltipXaxis,tooltipYaxis,setOpenTooltip,openTooltip,pulseAnimationsH,hotspotGuideMetaData,selectedTemplateTour,selectedOption,ProgressColor}=useDrawerStore(state=>state);const[targetElement,setTargetElement]=useState(null);// State to track if the popover should be shown\n// State for popup visibility is managed through openTooltip\nconst[popupPosition,setPopupPosition]=useState(null);const[hotspotSize,setHotspotSize]=useState(30);// Track hotspot size for dynamic popup positioning\nconst contentRef=useRef(null);const buttonContainerRef=useRef(null);let hotspot;const getElementByXPath=xpath=>{const result=document.evaluate(xpath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const node=result.singleNodeValue;if(node instanceof HTMLElement){return node;}else if(node!==null&&node!==void 0&&node.parentElement){return node.parentElement;// Return parent if it's a text node\n}else{return null;}};let xpath;if(savedGuideData)xpath=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide=savedGuideData.GuideStep)===null||_savedGuideData$Guide===void 0?void 0:(_savedGuideData$Guide2=_savedGuideData$Guide[0])===null||_savedGuideData$Guide2===void 0?void 0:_savedGuideData$Guide2.ElementPath;const getElementPosition=xpath=>{const element=getElementByXPath(xpath||\"\");if(element){const rect=element.getBoundingClientRect();return{top:rect.top,//+ window.scrollY + yOffset, // Adjust for vertical scroll\nleft:rect.left// + window.scrollX + xOffset, // Adjust for horizontal scroll\n};}return null;};// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const scrollbarRef=useRef(null);// Function to get estimated popup dimensions\nconst getEstimatedPopupDimensions=()=>{// Try to get actual dimensions from content if available\nif(contentRef.current){const contentRect=contentRef.current.getBoundingClientRect();return{width:Math.max(contentRect.width,50),// Minimum width\nheight:Math.max(contentRect.height+100,150)// Add padding for buttons/progress\n};}// Use the new width calculation logic for more accurate estimation\nconst widthStyling=getWidthStyling();let estimatedWidth=300;// Default fallback\nif(widthStyling.width==='auto'){// For auto-width, estimate based on content but respect maxWidth\nconst maxWidth=parseInt(widthStyling.maxWidth)||300;// Estimate content width (this could be improved with actual content measurement)\nestimatedWidth=Math.min(maxWidth,250);// Conservative estimate for auto-sizing\n}else{// For fixed width, use the specified width\nestimatedWidth=parseInt(widthStyling.width)||300;}const estimatedHeight=250;// Standard height for all content types\nreturn{width:estimatedWidth,height:estimatedHeight};};// Function to calculate smart popup position that stays within viewport\nconst calculatePopupPosition=(elementRect,hotspotSize,xOffset,yOffset)=>{const hotspotLeft=elementRect.x+xOffset;const hotspotTop=elementRect.y+yOffset;// Get viewport dimensions\nconst viewportWidth=window.innerWidth;const viewportHeight=window.innerHeight;// Get estimated popup dimensions\nconst{width:popupWidth,height:popupHeight}=getEstimatedPopupDimensions();// Viewport margin to ensure tooltip doesn't touch edges\nconst VIEWPORT_MARGIN=20;// Gap between hotspot and tooltip\nconst TOOLTIP_GAP=10;// Special gap for tooltips positioned above hotspots (reduced for better visual alignment)\nconst TOOLTIP_GAP_ABOVE=20;// Handle very small viewports - reduce margins if popup is too large\nconst availableWidth=viewportWidth-VIEWPORT_MARGIN*2;const availableHeight=viewportHeight-VIEWPORT_MARGIN*2;const effectiveMargin=Math.min(VIEWPORT_MARGIN,Math.max(5,Math.min((availableWidth-popupWidth)/2,(availableHeight-popupHeight)/2)));// Calculate hotspot bounds\nconst hotspotRight=hotspotLeft+hotspotSize;const hotspotBottom=hotspotTop+hotspotSize;// Define possible positions in order of preference\nconst positions=[// Default: bottom-right\n{name:'bottom-right',left:hotspotRight+TOOLTIP_GAP,top:hotspotBottom+TOOLTIP_GAP},// Alternative: bottom-left\n{name:'bottom-left',left:hotspotLeft-popupWidth-TOOLTIP_GAP,top:hotspotBottom+TOOLTIP_GAP},// Alternative: top-right (with 20px gap above hotspot)\n{name:'top-right',left:hotspotRight+TOOLTIP_GAP,top:hotspotTop-popupHeight-TOOLTIP_GAP_ABOVE},// Alternative: top-left (keep original small gap for left corner)\n{name:'top-left',left:hotspotLeft-popupWidth-TOOLTIP_GAP,top:hotspotTop-popupHeight-TOOLTIP_GAP},// Alternative: right-center\n{name:'right-center',left:hotspotRight+TOOLTIP_GAP,top:hotspotTop+hotspotSize/2-popupHeight/2},// Alternative: left-center\n{name:'left-center',left:hotspotLeft-popupWidth-TOOLTIP_GAP,top:hotspotTop+hotspotSize/2-popupHeight/2}];// Function to check if a position fits within viewport\nconst isPositionValid=pos=>{const right=pos.left+popupWidth;const bottom=pos.top+popupHeight;return pos.left>=effectiveMargin&&pos.top>=effectiveMargin&&right<=viewportWidth-effectiveMargin&&bottom<=viewportHeight-effectiveMargin;};// Find the first valid position\nlet selectedPosition=positions.find(pos=>isPositionValid(pos));// If no position fits perfectly, use the default and adjust to fit\nif(!selectedPosition){selectedPosition=positions[0];// Default: bottom-right\n// Adjust horizontally if needed\nif(selectedPosition.left+popupWidth>viewportWidth-effectiveMargin){selectedPosition.left=viewportWidth-popupWidth-effectiveMargin;}if(selectedPosition.left<effectiveMargin){selectedPosition.left=effectiveMargin;}// Adjust vertically if needed\nif(selectedPosition.top+popupHeight>viewportHeight-effectiveMargin){selectedPosition.top=viewportHeight-popupHeight-effectiveMargin;}if(selectedPosition.top<effectiveMargin){selectedPosition.top=effectiveMargin;}}// Debug logging (can be removed in production)\nif(process.env.NODE_ENV==='development'){console.log('Smart positioning:',{hotspotPosition:{left:hotspotLeft,top:hotspotTop},hotspotSize:hotspotSize,popupDimensions:{width:popupWidth,height:popupHeight},viewport:{width:viewportWidth,height:viewportHeight},selectedPosition:selectedPosition.name,finalPosition:{left:selectedPosition.left,top:selectedPosition.top}});}return{top:selectedPosition.top+window.scrollY,left:selectedPosition.left+window.scrollX};};useEffect(()=>{const element=getElementByXPath(xpath);if(element){const rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,// Account for scrolling\nleft:rect.left+window.scrollX});}},[xpath]);useEffect(()=>{if(typeof window!==undefined){const position=getElementPosition(xpath||\"\");if(position){setPopupPosition(position);}}},[xpath]);useEffect(()=>{const element=getElementByXPath(xpath);// setTargetElement(element);\nif(element){}},[savedGuideData]);useEffect(()=>{var _guideStep;const element=getElementByXPath(guideStep===null||guideStep===void 0?void 0:(_guideStep=guideStep[currentStep-1])===null||_guideStep===void 0?void 0:_guideStep.ElementPath);setTargetElement(element);if(element){element.style.backgroundColor=\"red !important\";// Update popup position when target element changes\nconst rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,left:rect.left+window.scrollX});}},[guideStep,currentStep]);// Hotspot styles are applied directly in the applyHotspotStyles function\n// State for overlay value\nconst[,setOverlayValue]=useState(false);const handleContinue=()=>{if(selectedTemplate!==\"Tour\"){if(currentStep<totalSteps){setCurrentStep(currentStep+1);onContinue();renderNextPopup(currentStep<totalSteps);}}else{setCurrentStep(currentStep+1);const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.style.display=\"none\";existingHotspot.remove();}}};const renderNextPopup=shouldRenderNextPopup=>{var _savedGuideData$Guide3,_savedGuideData$Guide4,_savedGuideData$Guide5,_savedGuideData$Guide6,_savedGuideData$Guide7,_savedGuideData$Guide8,_savedGuideData$Guide9,_savedGuideData$Guide10,_savedGuideData$Guide11,_savedGuideData$Guide12;return shouldRenderNextPopup?/*#__PURE__*/_jsx(HotspotPreview,{isHotspotPopupOpen:isHotspotPopupOpen,showHotspotenduser:showHotspotenduser,handleHotspotHover:handleHotspotHover,handleHotspotClick:handleHotspotClick,anchorEl:anchorEl,savedGuideData:savedGuideData,guideStep:guideStep,onClose:onClose,onPrevious:handlePrevious,onContinue:handleContinue,title:title,text:text,imageUrl:imageUrl,currentStep:currentStep+1,totalSteps:totalSteps,onDontShowAgain:onDontShowAgain,progress:progress,textFieldProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide3=savedGuideData.GuideStep)===null||_savedGuideData$Guide3===void 0?void 0:(_savedGuideData$Guide4=_savedGuideData$Guide3[currentStep])===null||_savedGuideData$Guide4===void 0?void 0:_savedGuideData$Guide4.TextFieldProperties,imageProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide5=savedGuideData.GuideStep)===null||_savedGuideData$Guide5===void 0?void 0:(_savedGuideData$Guide6=_savedGuideData$Guide5[currentStep])===null||_savedGuideData$Guide6===void 0?void 0:_savedGuideData$Guide6.ImageProperties,customButton:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide7=savedGuideData.GuideStep)===null||_savedGuideData$Guide7===void 0?void 0:(_savedGuideData$Guide8=_savedGuideData$Guide7[currentStep])===null||_savedGuideData$Guide8===void 0?void 0:(_savedGuideData$Guide9=_savedGuideData$Guide8.ButtonSection)===null||_savedGuideData$Guide9===void 0?void 0:(_savedGuideData$Guide10=_savedGuideData$Guide9.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_savedGuideData$Guide10===void 0?void 0:_savedGuideData$Guide10.reduce((acc,curr)=>acc.concat(curr),[]))||[],modalProperties:modalProperties,canvasProperties:canvasProperties,htmlSnippet:htmlSnippet,OverlayValue:OverlayValue,hotspotProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide11=savedGuideData.GuideStep)===null||_savedGuideData$Guide11===void 0?void 0:(_savedGuideData$Guide12=_savedGuideData$Guide11[currentStep-1])===null||_savedGuideData$Guide12===void 0?void 0:_savedGuideData$Guide12.Hotspot)||{}}):null;};const handlePrevious=()=>{if(currentStep>1){setCurrentStep(currentStep-1);onPrevious();}};useEffect(()=>{if(OverlayValue){setOverlayValue(true);}else{setOverlayValue(false);}},[OverlayValue]);// Image fit is used directly in the component\nconst getAnchorAndTransformOrigins=position=>{switch(position){case\"top-left\":return{anchorOrigin:{vertical:\"top\",horizontal:\"left\"},transformOrigin:{vertical:\"bottom\",horizontal:\"right\"}};case\"top-right\":return{anchorOrigin:{vertical:\"top\",horizontal:\"right\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"}};case\"bottom-left\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"left\"},transformOrigin:{vertical:\"top\",horizontal:\"right\"}};case\"bottom-right\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};case\"center-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"top-center\":return{anchorOrigin:{vertical:\"top\",horizontal:\"center\"},transformOrigin:{vertical:\"bottom\",horizontal:\"center\"}};case\"left-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"left\"},transformOrigin:{vertical:\"center\",horizontal:\"right\"}};case\"bottom-center\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"right-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};default:return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};}};const{anchorOrigin,transformOrigin}=getAnchorAndTransformOrigins((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center center\");const textStyle={fontWeight:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$=textFieldProperties.TextProperties)!==null&&_textFieldProperties$!==void 0&&_textFieldProperties$.Bold?\"bold\":\"normal\",fontStyle:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$2=textFieldProperties.TextProperties)!==null&&_textFieldProperties$2!==void 0&&_textFieldProperties$2.Italic?\"italic\":\"normal\",color:(textFieldProperties===null||textFieldProperties===void 0?void 0:(_textFieldProperties$3=textFieldProperties.TextProperties)===null||_textFieldProperties$3===void 0?void 0:_textFieldProperties$3.TextColor)||\"#000000\",textAlign:(textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.Alignment)||\"left\"};// Image styles are applied directly in the component\nconst renderHtmlSnippet=snippet=>{// Return the raw HTML snippet for rendering\nreturn{__html:snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g,(_match,p1,p2,p3)=>{return`${p1}${p2}\" target=\"_blank\"${p3}`;})};};// Function to check if canvas width has been modified from default\nconst isCanvasWidthModified=()=>{var _canvasProperties$Wid;const defaultWidth=\"300\";// Default width without \"px\"\nconst currentWidth=(canvasProperties===null||canvasProperties===void 0?void 0:(_canvasProperties$Wid=canvasProperties.Width)===null||_canvasProperties$Wid===void 0?void 0:_canvasProperties$Wid.replace(\"px\",\"\"))||defaultWidth;return currentWidth!==defaultWidth;};// Function to get width styling based on user preferences\nconst getWidthStyling=()=>{// Check if user has modified canvas width from default\nconst hasCustomWidth=isCanvasWidthModified();if(hasCustomWidth&&canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width){// User-specified width: use exact width, override auto-sizing and max-width\nconst userWidth=canvasProperties.Width.includes('px')?canvasProperties.Width:`${canvasProperties.Width}px`;return{width:userWidth,maxWidth:userWidth,// Override the 300px limit\nminWidth:'unset'// Remove any minimum width constraints\n};}else{// Default behavior: auto-adjust with 300px maximum, no minimum width\nreturn{width:'auto',// Allow auto-sizing based on content\nmaxWidth:'300px',// Maximum width constraint\nminWidth:'unset'// Remove minimum width constraints for small content\n};}};// Update popup position when content changes (width is now calculated dynamically)\nuseEffect(()=>{// Recalculate popup position when content changes since width affects positioning\nif(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData===void 0?void 0:_toolTipGuideMetaData.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}},[textFieldProperties,imageProperties,customButton,currentStep,xpath,hotspotSize,toolTipGuideMetaData]);// Recalculate popup position when hotspot size changes or content dimensions change\nuseEffect(()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData2;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData2=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}},[hotspotSize,xpath,toolTipGuideMetaData,textFieldProperties,imageProperties,customButton]);// Recalculate popup position on window resize\nuseEffect(()=>{const handleResize=()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData3;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData3=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData3===void 0?void 0:_toolTipGuideMetaData3.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}};window.addEventListener('resize',handleResize);return()=>window.removeEventListener('resize',handleResize);},[xpath,hotspotSize,toolTipGuideMetaData]);const groupedButtons=customButton.reduce((acc,button)=>{const containerId=button.ContainerId||\"default\";// Use a ContainerId or fallback\nif(!acc[containerId]){acc[containerId]=[];}acc[containerId].push(button);return acc;},{});const widthStyling=getWidthStyling();const canvasStyle={position:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\",borderRadius:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Radius)||\"4px\",borderWidth:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderSize)||\"0px\",borderColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderColor)||\"black\",borderStyle:\"solid\",backgroundColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BackgroundColor)||\"white\",...widthStyling// Apply the width styling (width, maxWidth, minWidth)\n};const sectionHeight=((_imageProperties=imageProperties[currentStep-1])===null||_imageProperties===void 0?void 0:(_imageProperties$Cust=_imageProperties.CustomImage)===null||_imageProperties$Cust===void 0?void 0:(_imageProperties$Cust2=_imageProperties$Cust[currentStep-1])===null||_imageProperties$Cust2===void 0?void 0:_imageProperties$Cust2.SectionHeight)||\"auto\";const handleButtonAction=action=>{if(action.Action===\"open-url\"||action.Action===\"open\"||action.Action===\"openurl\"){const targetUrl=action.TargetUrl;if(action.ActionValue===\"same-tab\"){// Open the URL in the same tab\nwindow.location.href=targetUrl;}else{// Open the URL in a new tab\nwindow.open(targetUrl,\"_blank\",\"noopener noreferrer\");}}else{if(action.Action==\"Previous\"||action.Action==\"previous\"||action.ActionValue==\"Previous\"||action.ActionValue==\"Previous\"){handlePrevious();}else if(action.Action==\"Next\"||action.Action==\"next\"||action.ActionValue==\"Next\"||action.ActionValue==\"next\"){handleContinue();}else if(action.Action==\"Restart\"||action.ActionValue==\"Restart\"){var _savedGuideData$Guide13,_savedGuideData$Guide14;// Reset to the first step\nsetCurrentStep(1);// If there's a specific URL for the first step, navigate to it\nif(savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide13=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide13!==void 0&&(_savedGuideData$Guide14=_savedGuideData$Guide13[0])!==null&&_savedGuideData$Guide14!==void 0&&_savedGuideData$Guide14.ElementPath){const firstStepElement=getElementByXPath(savedGuideData.GuideStep[0].ElementPath);if(firstStepElement){firstStepElement.scrollIntoView({behavior:'smooth'});}}}}setOverlayValue(false);};useEffect(()=>{var _guideStep2,_guideStep2$Hotspot;if(guideStep!==null&&guideStep!==void 0&&(_guideStep2=guideStep[currentStep-1])!==null&&_guideStep2!==void 0&&(_guideStep2$Hotspot=_guideStep2.Hotspot)!==null&&_guideStep2$Hotspot!==void 0&&_guideStep2$Hotspot.ShowByDefault){// Show tooltip by default\nsetOpenTooltip(true);}},[guideStep===null||guideStep===void 0?void 0:guideStep[currentStep-1],currentStep,setOpenTooltip]);// Add effect to handle isHotspotPopupOpen prop changes\nuseEffect(()=>{if(isHotspotPopupOpen){var _toolTipGuideMetaData4,_toolTipGuideMetaData5,_savedGuideData$Guide15,_savedGuideData$Guide16,_savedGuideData$Guide17,_savedGuideData$Guide18;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData4=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData4!==void 0&&_toolTipGuideMetaData4.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData5=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData5===void 0?void 0:_toolTipGuideMetaData5.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide15=savedGuideData.GuideStep)===null||_savedGuideData$Guide15===void 0?void 0:(_savedGuideData$Guide16=_savedGuideData$Guide15[currentStep-1])===null||_savedGuideData$Guide16===void 0?void 0:_savedGuideData$Guide16.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide17=savedGuideData.GuideStep)===null||_savedGuideData$Guide17===void 0?void 0:(_savedGuideData$Guide18=_savedGuideData$Guide17[0])===null||_savedGuideData$Guide18===void 0?void 0:_savedGuideData$Guide18.Hotspot;// Only show tooltip by default if ShowByDefault is true\n// For \"Hovering Hotspot\", we'll wait for the hover event\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[isHotspotPopupOpen,toolTipGuideMetaData]);// Add effect to handle showHotspotenduser prop changes\nuseEffect(()=>{if(showHotspotenduser){var _toolTipGuideMetaData6,_toolTipGuideMetaData7,_savedGuideData$Guide19,_savedGuideData$Guide20,_savedGuideData$Guide21,_savedGuideData$Guide22;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData6=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData6!==void 0&&_toolTipGuideMetaData6.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData7=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData7===void 0?void 0:_toolTipGuideMetaData7.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide19=savedGuideData.GuideStep)===null||_savedGuideData$Guide19===void 0?void 0:(_savedGuideData$Guide20=_savedGuideData$Guide19[currentStep-1])===null||_savedGuideData$Guide20===void 0?void 0:_savedGuideData$Guide20.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide21=savedGuideData.GuideStep)===null||_savedGuideData$Guide21===void 0?void 0:(_savedGuideData$Guide22=_savedGuideData$Guide21[0])===null||_savedGuideData$Guide22===void 0?void 0:_savedGuideData$Guide22.Hotspot;// Only show tooltip by default if ShowByDefault is true\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[showHotspotenduser,toolTipGuideMetaData]);// Add a global click handler to detect clicks outside the hotspot to close the tooltip\nuseEffect(()=>{const handleGlobalClick=e=>{const hotspotElement=document.getElementById(\"hotspotBlink\");// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\nif(hotspotElement&&hotspotElement.contains(e.target)){return;}// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on clicks outside anymore\n};document.addEventListener(\"click\",handleGlobalClick);return()=>{document.removeEventListener(\"click\",handleGlobalClick);};},[toolTipGuideMetaData]);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(contentRef.current){// Force a reflow to get accurate measurements\ncontentRef.current.style.height='auto';const contentHeight=contentRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(contentRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(contentRef.current);}if(contentRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(contentRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[currentStep]);// We no longer need the persistent monitoring effect since we want the tooltip\n// to close when the mouse leaves the hotspot\nfunction getAlignment(alignment){switch(alignment){case\"start\":return\"flex-start\";case\"end\":return\"flex-end\";case\"center\":default:return\"center\";}}const getCanvasPosition=function(){let position=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"center-center\";switch(position){case\"bottom-left\":return{top:\"auto !important\"};case\"bottom-right\":return{top:\"auto !important\"};case\"bottom-center\":return{top:\"auto !important\"};case\"center-center\":return{top:\"25% !important\"};case\"left-center\":return{top:imageUrl===\"\"?\"40% !important\":\"20% !important\"};case\"right-center\":return{top:\"10% !important\"};case\"top-left\":return{top:\"10% !important\"};case\"top-right\":return{top:\"10% !important\"};case\"top-center\":return{top:\"9% !important\"};default:return{top:\"25% !important\"};}};// function to get the correct property value based on tour vs normal hotspot\nconst getHotspotProperty=(propName,hotspotPropData,hotspotData)=>{if(selectedTemplateTour===\"Hotspot\"){// For tour hotspots, use saved data first, fallback to metadata\nswitch(propName){case'PulseAnimation':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.PulseAnimation)!==undefined?hotspotData.PulseAnimation:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.PulseAnimation;case'StopAnimation':// Always use stopAnimationUponInteraction for consistency\nreturn(hotspotData===null||hotspotData===void 0?void 0:hotspotData.stopAnimationUponInteraction)!==undefined?hotspotData.stopAnimationUponInteraction:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;case'ShowUpon':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowUpon)!==undefined?hotspotData.ShowUpon:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowUpon;case'ShowByDefault':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowByDefault)!==undefined?hotspotData.ShowByDefault:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowByDefault;default:return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}}else{// For normal hotspots, use metadata\nif(propName==='StopAnimation'){return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;}return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}};const applyHotspotStyles=(hotspot,hotspotPropData,hotspotData,left,top)=>{hotspot.style.position=\"absolute\";hotspot.style.left=`${left}px`;hotspot.style.top=`${top}px`;hotspot.style.width=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;// Default size if not provided\nhotspot.style.height=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;hotspot.style.backgroundColor=hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Color;hotspot.style.borderRadius=\"50%\";hotspot.style.zIndex=\"auto !important\";// Increased z-index\nhotspot.style.transition=\"none\";hotspot.style.pointerEvents=\"auto\";// Ensure clicks are registered\nhotspot.innerHTML=\"\";if((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Info\"||(hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Question\"){const textSpan=document.createElement(\"span\");textSpan.innerText=hotspotPropData.Type===\"Info\"?\"i\":\"?\";textSpan.style.color=\"white\";textSpan.style.fontSize=\"14px\";textSpan.style.fontWeight=\"bold\";textSpan.style.fontStyle=hotspotPropData.Type===\"Info\"?\"italic\":\"normal\";textSpan.style.display=\"flex\";textSpan.style.alignItems=\"center\";textSpan.style.justifyContent=\"center\";textSpan.style.width=\"100%\";textSpan.style.height=\"100%\";hotspot.appendChild(textSpan);}// Apply animation class if needed\n// Track if pulse has been stopped by hover\nconst pulseAnimationEnabled=getHotspotProperty('PulseAnimation',hotspotPropData,hotspotData);const shouldPulse=selectedTemplateTour===\"Hotspot\"?pulseAnimationEnabled!==false&&!hotspot._pulseStopped:hotspotPropData&&pulseAnimationsH&&!hotspot._pulseStopped;if(shouldPulse){hotspot.classList.add(\"pulse-animation\");hotspot.classList.remove(\"pulse-animation-removed\");}else{hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");}// Ensure the hotspot is visible and clickable\nhotspot.style.display=\"flex\";hotspot.style.pointerEvents=\"auto\";// No need for separate animation control functions here\n// Animation will be controlled directly in the event handlers\n// Set initial state of openTooltip based on ShowByDefault and ShowUpon\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{// If not showing by default, only show based on interaction type\n//setOpenTooltip(false);\n}// Only clone and replace if the hotspot doesn't have event listeners already\n// This prevents losing the _pulseStopped state unnecessarily\nif(!hotspot.hasAttribute('data-listeners-attached')){const newHotspot=hotspot.cloneNode(true);// Copy the _pulseStopped property if it exists\nif(hotspot._pulseStopped!==undefined){newHotspot._pulseStopped=hotspot._pulseStopped;}if(hotspot.parentNode){hotspot.parentNode.replaceChild(newHotspot,hotspot);hotspot=newHotspot;}}// Ensure pointer events are enabled\nhotspot.style.pointerEvents=\"auto\";// Define combined event handlers that handle both animation and tooltip\nconst showUpon=getHotspotProperty('ShowUpon',hotspotPropData,hotspotData);const handleHover=e=>{e.stopPropagation();console.log(\"Hover detected on hotspot\");// Show tooltip if ShowUpon is \"Hovering Hotspot\"\nif(showUpon===\"Hovering Hotspot\"){// Set openTooltip to true when hovering\nsetOpenTooltip(true);// Call the passed hover handler if it exists\nif(typeof handleHotspotHover===\"function\"){handleHotspotHover();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};const handleMouseOut=e=>{e.stopPropagation();// Hide tooltip when mouse leaves the hotspot\n// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showUpon===\"Hovering Hotspot\"&&!showByDefault){// setOpenTooltip(false);\n}};const handleClick=e=>{e.stopPropagation();console.log(\"Click detected on hotspot\");// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\nif(showUpon===\"Clicking Hotspot\"||!showUpon){// Toggle the tooltip state\nsetOpenTooltip(!openTooltip);// Call the passed click handler if it exists\nif(typeof handleHotspotClick===\"function\"){handleHotspotClick();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};// Add appropriate event listeners based on ShowUpon property\nif(!hotspot.hasAttribute('data-listeners-attached')){if(showUpon===\"Hovering Hotspot\"){// For hover interaction\nhotspot.addEventListener(\"mouseover\",handleHover);hotspot.addEventListener(\"mouseout\",handleMouseOut);// Also add click handler for better user experience\nhotspot.addEventListener(\"click\",handleClick);}else{// For click interaction (default)\nhotspot.addEventListener(\"click\",handleClick);}// Mark that listeners have been attached\nhotspot.setAttribute('data-listeners-attached','true');}};useEffect(()=>{let element;let steps;const fetchGuideDetails=async()=>{try{var _savedGuideData$Guide23,_savedGuideData$Guide24,_steps,_steps$;//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\nsteps=(savedGuideData===null||savedGuideData===void 0?void 0:savedGuideData.GuideStep)||[];// For tour hotspots, use the current step's element path\nconst elementPath=selectedTemplateTour===\"Hotspot\"&&savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide23=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide23!==void 0&&(_savedGuideData$Guide24=_savedGuideData$Guide23[currentStep-1])!==null&&_savedGuideData$Guide24!==void 0&&_savedGuideData$Guide24.ElementPath?savedGuideData.GuideStep[currentStep-1].ElementPath:((_steps=steps)===null||_steps===void 0?void 0:(_steps$=_steps[0])===null||_steps$===void 0?void 0:_steps$.ElementPath)||\"\";element=getElementByXPath(elementPath||\"\");setTargetElement(element);if(element){// element.style.outline = \"2px solid red\";\n}// Check if this is a hotspot scenario (normal or tour)\nconst isHotspotScenario=selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"||title===\"Hotspot\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Hotspot\";if(isHotspotScenario){var _toolTipGuideMetaData8,_toolTipGuideMetaData9,_hotspotPropData,_hotspotPropData2,_hotspotPropData3,_hotspotPropData4;// Get hotspot properties - prioritize tour data for tour hotspots\nlet hotspotPropData;let hotspotData;if(selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData8=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData8!==void 0&&_toolTipGuideMetaData8.hotspots){var _savedGuideData$Guide25,_savedGuideData$Guide26;// Tour hotspot - use current step metadata\nhotspotPropData=toolTipGuideMetaData[currentStep-1].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide25=savedGuideData.GuideStep)===null||_savedGuideData$Guide25===void 0?void 0:(_savedGuideData$Guide26=_savedGuideData$Guide25[currentStep-1])===null||_savedGuideData$Guide26===void 0?void 0:_savedGuideData$Guide26.Hotspot;}else if(toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData9=toolTipGuideMetaData[0])!==null&&_toolTipGuideMetaData9!==void 0&&_toolTipGuideMetaData9.hotspots){var _savedGuideData$Guide27,_savedGuideData$Guide28;// Normal hotspot - use first metadata entry\nhotspotPropData=toolTipGuideMetaData[0].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide27=savedGuideData.GuideStep)===null||_savedGuideData$Guide27===void 0?void 0:(_savedGuideData$Guide28=_savedGuideData$Guide27[0])===null||_savedGuideData$Guide28===void 0?void 0:_savedGuideData$Guide28.Hotspot;}else{var _savedGuideData$Guide29,_savedGuideData$Guide30;// Fallback to default values for tour hotspots without metadata\nhotspotPropData={XPosition:\"4\",YPosition:\"4\",Type:\"Question\",Color:\"yellow\",Size:\"16\",PulseAnimation:true,stopAnimationUponInteraction:true,ShowUpon:\"Hovering Hotspot\",ShowByDefault:false};hotspotData=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide29=savedGuideData.GuideStep)===null||_savedGuideData$Guide29===void 0?void 0:(_savedGuideData$Guide30=_savedGuideData$Guide29[currentStep-1])===null||_savedGuideData$Guide30===void 0?void 0:_savedGuideData$Guide30.Hotspot)||{};}const xOffset=parseFloat(((_hotspotPropData=hotspotPropData)===null||_hotspotPropData===void 0?void 0:_hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat(((_hotspotPropData2=hotspotPropData)===null||_hotspotPropData2===void 0?void 0:_hotspotPropData2.YPosition)||\"4\");const currentHotspotSize=parseFloat(((_hotspotPropData3=hotspotPropData)===null||_hotspotPropData3===void 0?void 0:_hotspotPropData3.Size)||\"30\");// Update hotspot size state\nsetHotspotSize(currentHotspotSize);let left,top;if(element){const rect=element.getBoundingClientRect();left=rect.x+xOffset;top=rect.y+(yOffset>0?-yOffset:Math.abs(yOffset));// Calculate popup position below the hotspot\nconst popupPos=calculatePopupPosition(rect,currentHotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}// Check if hotspot already exists, preserve it to maintain _pulseStopped state\nconst existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){hotspot=existingHotspot;// Don't reset _pulseStopped if it already exists\n}else{// Create new hotspot only if it doesn't exist\nhotspot=document.createElement(\"div\");hotspot.id=\"hotspotBlink\";// Fixed ID for easier reference\nhotspot._pulseStopped=false;// Set only on creation\ndocument.body.appendChild(hotspot);}hotspot.style.cursor=\"pointer\";hotspot.style.pointerEvents=\"auto\";// Ensure it can receive mouse events\n// Make sure the hotspot is visible and clickable\nhotspot.style.zIndex=\"9999\";// If ShowByDefault is true, set openTooltip to true immediately\nif((_hotspotPropData4=hotspotPropData)!==null&&_hotspotPropData4!==void 0&&_hotspotPropData4.ShowByDefault){setOpenTooltip(true);}// Set styles first\napplyHotspotStyles(hotspot,hotspotPropData,hotspotData,left,top);// Set initial tooltip visibility based on ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{//setOpenTooltip(false);\n}// We don't need to add event listeners here as they're already added in applyHotspotStyles\n}}catch(error){console.error(\"Error in fetchGuideDetails:\",error);}};fetchGuideDetails();return()=>{const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.onclick=null;existingHotspot.onmouseover=null;existingHotspot.onmouseout=null;}};},[savedGuideData,toolTipGuideMetaData,isHotspotPopupOpen,showHotspotenduser,selectedTemplateTour,currentStep// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n]);const enableProgress=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide31=savedGuideData.GuideStep)===null||_savedGuideData$Guide31===void 0?void 0:(_savedGuideData$Guide32=_savedGuideData$Guide31[0])===null||_savedGuideData$Guide32===void 0?void 0:(_savedGuideData$Guide33=_savedGuideData$Guide32.Tooltip)===null||_savedGuideData$Guide33===void 0?void 0:_savedGuideData$Guide33.EnableProgress)||false;function getProgressTemplate(selectedOption){var _savedGuideData$Guide34,_savedGuideData$Guide35,_savedGuideData$Guide36;if(selectedOption===1){return\"dots\";}else if(selectedOption===2){return\"linear\";}else if(selectedOption===3){return\"BreadCrumbs\";}else if(selectedOption===4){return\"breadcrumbs\";}return(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide34=savedGuideData.GuideStep)===null||_savedGuideData$Guide34===void 0?void 0:(_savedGuideData$Guide35=_savedGuideData$Guide34[0])===null||_savedGuideData$Guide35===void 0?void 0:(_savedGuideData$Guide36=_savedGuideData$Guide35.Tooltip)===null||_savedGuideData$Guide36===void 0?void 0:_savedGuideData$Guide36.ProgressTemplate)||\"dots\";}const progressTemplate=getProgressTemplate(selectedOption);const renderProgress=()=>{if(!enableProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:totalSteps,position:\"static\",activeStep:currentStep-1,sx:{backgroundColor:\"transparent\",position:\"inherit !important\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n}},backButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}})});}if(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"5px\",padding:\"8px\"},children:Array.from({length:totalSteps}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:\"14px\",height:\"4px\",backgroundColor:index===currentStep-1?ProgressColor:\"#e0e0e0\",// Active color and inactive color\nborderRadius:\"100px\"}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"flex-start\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{padding:\"8px\",color:ProgressColor},children:[\"Step \",currentStep,\" of \",totalSteps]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:\"6px\",borderRadius:\"20px\",margin:\"6px 10px\",\"& .MuiLinearProgress-bar\":{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};return/*#__PURE__*/_jsxs(_Fragment,{children:[targetElement&&/*#__PURE__*/_jsx(\"div\",{children:openTooltip&&/*#__PURE__*/_jsxs(Popover,{open:Boolean(popupPosition)||Boolean(anchorEl),anchorEl:anchorEl,onClose:()=>{// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on Popover close events\n},anchorOrigin:anchorOrigin,transformOrigin:transformOrigin,anchorReference:\"anchorPosition\",anchorPosition:popupPosition?{top:popupPosition.top+(parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\"))),left:popupPosition.left+parseFloat(tooltipXaxis||\"0\")}:undefined,sx:{// \"& .MuiBackdrop-root\": {\n//     position: 'relative !important', // Ensures higher specificity\n// },\n\"pointer-events\":anchorEl?\"auto\":\"auto\",'& .MuiPaper-root:not(.MuiMobileStepper-root)':{zIndex:1000,// borderRadius: \"1px\",\n...canvasStyle,//...getAnchorAndTransformOrigins,\n//top: \"16% !important\",\n// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\n//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\n//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\n//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\n//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\n//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\n//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\n//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\n//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\n...getCanvasPosition((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\"),top:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.top)||0)+(tooltipYaxis&&tooltipYaxis!='undefined'?parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\")):0)}px !important`,left:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.left)||0)+(tooltipXaxis&&tooltipXaxis!='undefined'?parseFloat(tooltipXaxis)||0:0)}px !important`,overflow:\"hidden\",// Add smooth transitions for position changes\ntransition:'top 0.3s ease-out, left 0.3s ease-out'}},disableScrollLock:true,children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:(modalProperties===null||modalProperties===void 0?void 0:modalProperties.DismissOption)&&/*#__PURE__*/_jsx(IconButton,{onClick:()=>{// Only close if explicitly requested by user clicking the close button\n//setOpenTooltip(false);\n},sx:{position:\"fixed\",boxShadow:\"rgba(0, 0, 0, 0.06) 0px 4px 8px\",left:\"auto\",right:\"auto\",margin:\"-15px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",borderRadius:\"50px\",padding:\"5px !important\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:1,color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:\"400px\"},options:{suppressScrollY:!needsScrolling,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:\"400px\",overflow:\"hidden auto\"},children:/*#__PURE__*/_jsxs(Box,{style:{padding:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Padding)||\"10px\",height:sectionHeight},children:[/*#__PURE__*/_jsxs(Box,{ref:contentRef,display:\"flex\",flexDirection:\"column\",flexWrap:\"wrap\",justifyContent:\"center\",sx:{width:\"100%\",padding:\"8px\",boxSizing:\"border-box\"},children:[imageProperties===null||imageProperties===void 0?void 0:imageProperties.map(imageProp=>imageProp.CustomImage.map((customImg,imgIndex)=>/*#__PURE__*/_jsx(Box,{component:\"img\",src:customImg.Url,alt:customImg.AltText||\"Image\",sx:{maxHeight:imageProp.MaxImageHeight||customImg.MaxImageHeight||\"500px\",textAlign:imageProp.Alignment||\"center\",objectFit:customImg.Fit||\"contain\",//  width: \"500px\",\nheight:`${customImg.SectionHeight||250}px`,background:customImg.BackgroundColor||\"#ffffff\",margin:\"10px 0\"},onClick:()=>{if(imageProp.Hyperlink){const targetUrl=imageProp.Hyperlink;window.open(targetUrl,\"_blank\",\"noopener noreferrer\");}},style:{cursor:imageProp.Hyperlink?\"pointer\":\"default\"}},`${imageProp.Id}-${imgIndex}`))),textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.map((textField,index)=>{var _textField$TextProper,_textField$TextProper2;return textField.Text&&/*#__PURE__*/_jsx(Typography,{className:\"qadpt-preview\",// Use a unique key, either Id or index\nsx:{textAlign:((_textField$TextProper=textField.TextProperties)===null||_textField$TextProper===void 0?void 0:_textField$TextProper.TextFormat)||textStyle.textAlign,color:((_textField$TextProper2=textField.TextProperties)===null||_textField$TextProper2===void 0?void 0:_textField$TextProper2.TextColor)||textStyle.color,whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",padding:\"0 5px\",wordWrap:\"break-word\",overflowWrap:\"break-word\",hyphens:\"auto\"},dangerouslySetInnerHTML:renderHtmlSnippet(textField.Text)// Render the raw HTML\n},textField.Id||index);})]}),Object.keys(groupedButtons).map(containerId=>{var _groupedButtons$conta,_groupedButtons$conta2;return/*#__PURE__*/_jsx(Box,{ref:buttonContainerRef,sx:{display:\"flex\",justifyContent:getAlignment((_groupedButtons$conta=groupedButtons[containerId][0])===null||_groupedButtons$conta===void 0?void 0:_groupedButtons$conta.Alignment),flexWrap:\"wrap\",margin:\"5px 0\",backgroundColor:(_groupedButtons$conta2=groupedButtons[containerId][0])===null||_groupedButtons$conta2===void 0?void 0:_groupedButtons$conta2.BackgroundColor,padding:\"5px 0\",width:\"100%\"},children:groupedButtons[containerId].map((button,index)=>{var _button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3,_button$ButtonPropert4,_button$ButtonPropert5,_button$ButtonPropert6,_button$ButtonPropert7;return/*#__PURE__*/_jsx(Button,{onClick:()=>handleButtonAction(button.ButtonAction),variant:\"contained\",sx:{margin:\"0 5px 5px 5px\",backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#007bff\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#fff\",border:((_button$ButtonPropert3=button.ButtonProperties)===null||_button$ButtonPropert3===void 0?void 0:_button$ButtonPropert3.ButtonBorderColor)||\"transparent\",fontSize:((_button$ButtonPropert4=button.ButtonProperties)===null||_button$ButtonPropert4===void 0?void 0:_button$ButtonPropert4.FontSize)||\"15px\",width:((_button$ButtonPropert5=button.ButtonProperties)===null||_button$ButtonPropert5===void 0?void 0:_button$ButtonPropert5.Width)||\"auto\",padding:\"4px 8px\",lineHeight:\"normal\",textTransform:\"none\",borderRadius:((_button$ButtonPropert6=button.ButtonProperties)===null||_button$ButtonPropert6===void 0?void 0:_button$ButtonPropert6.BorderRadius)||\"8px\",boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{backgroundColor:((_button$ButtonPropert7=button.ButtonProperties)===null||_button$ButtonPropert7===void 0?void 0:_button$ButtonPropert7.ButtonBackgroundColor)||\"#007bff\",// Keep the same background color on hover\nopacity:0.9,// Slightly reduce opacity on hover for visual feedback\nboxShadow:\"none !important\"// Remove box shadow in hover state\n}},children:button.ButtonName},index);})},containerId);})]})})},`scrollbar-${needsScrolling}`),enableProgress&&totalSteps>1&&selectedTemplate===\"Tour\"&&/*#__PURE__*/_jsx(Box,{children:renderProgress()}),\" \"]})}),/*#__PURE__*/_jsx(\"style\",{children:`\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `})]});};export default HotspotPreview;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "HotspotPreview", "_ref", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "hotspotSize", "setHotspotSize", "contentRef", "buttonContainerRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "getElementPosition", "element", "rect", "getBoundingClientRect", "top", "left", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "getEstimatedPopupDimensions", "current", "contentRect", "width", "Math", "max", "height", "widthStyling", "getWidthStyling", "estimatedWidth", "max<PERSON><PERSON><PERSON>", "parseInt", "min", "estimatedHeight", "calculatePopupPosition", "elementRect", "xOffset", "yOffset", "hotspotLeft", "x", "hotspotTop", "y", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "popup<PERSON><PERSON><PERSON>", "popupHeight", "VIEWPORT_MARGIN", "TOOLTIP_GAP", "TOOLTIP_GAP_ABOVE", "availableWidth", "availableHeight", "<PERSON><PERSON><PERSON>gin", "hotspotRight", "hotspotBottom", "positions", "name", "isPositionValid", "pos", "right", "bottom", "selectedPosition", "find", "process", "env", "NODE_ENV", "console", "log", "hotspotPosition", "popupDimensions", "viewport", "finalPosition", "scrollY", "scrollX", "undefined", "position", "_guideStep", "style", "backgroundColor", "setOverlayValue", "handleContinue", "renderNextPopup", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "Hotspot", "getAnchorAndTransformOrigins", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "replace", "_match", "p1", "p2", "p3", "isCanvasWidthModified", "_canvasProperties$Wid", "defaultWidth", "currentWidth", "<PERSON><PERSON><PERSON>", "hasCustomWidth", "userWidth", "includes", "min<PERSON><PERSON><PERSON>", "_toolTipGuideMetaData", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "popupPos", "_toolTipGuideMetaData2", "handleResize", "_toolTipGuideMetaData3", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "sectionHeight", "CustomImage", "SectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "firstStepElement", "scrollIntoView", "behavior", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_savedGuideData$Guide15", "_savedGuideData$Guide16", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "hotspotData", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "setTimeout", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "arguments", "length", "getHotspotProperty", "propName", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Size", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "currentHotspotSize", "abs", "id", "body", "cursor", "error", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "padding", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "boxShadow", "background", "border", "zoom", "ref", "maxHeight", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "boxSizing", "imageProp", "customImg", "imgIndex", "component", "src", "Url", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "Text", "className", "TextFormat", "whiteSpace", "wordBreak", "wordWrap", "overflowWrap", "hyphens", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// State to track if the popover should be shown\r\n\t// State for popup visibility is managed through openTooltip\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\tconst getElementPosition = (xpath: string | undefined) => {\r\n\t\tconst element = getElementByXPath(xpath || \"\");\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll\r\n\t\t\t\tleft: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to get estimated popup dimensions\r\n\tconst getEstimatedPopupDimensions = () => {\r\n\t\t// Try to get actual dimensions from content if available\r\n\t\tif (contentRef.current) {\r\n\t\t\tconst contentRect = contentRef.current.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\twidth: Math.max(contentRect.width, 50), // Minimum width\r\n\t\t\t\theight: Math.max(contentRect.height + 100, 150) // Add padding for buttons/progress\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\t// Use the new width calculation logic for more accurate estimation\r\n\t\tconst widthStyling = getWidthStyling();\r\n\t\tlet estimatedWidth = 300; // Default fallback\r\n\r\n\t\tif (widthStyling.width === 'auto') {\r\n\t\t\t// For auto-width, estimate based on content but respect maxWidth\r\n\t\t\tconst maxWidth = parseInt(widthStyling.maxWidth) || 300;\r\n\t\t\t// Estimate content width (this could be improved with actual content measurement)\r\n\t\t\testimatedWidth = Math.min(maxWidth, 250); // Conservative estimate for auto-sizing\r\n\t\t} else {\r\n\t\t\t// For fixed width, use the specified width\r\n\t\t\testimatedWidth = parseInt(widthStyling.width) || 300;\r\n\t\t}\r\n\r\n\t\tconst estimatedHeight = 250; // Standard height for all content types\r\n\r\n\t\treturn {\r\n\t\t\twidth: estimatedWidth,\r\n\t\t\theight: estimatedHeight\r\n\t\t};\r\n\t};\r\n\r\n\t// Function to calculate smart popup position that stays within viewport\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Get viewport dimensions\r\n\t\tconst viewportWidth = window.innerWidth;\r\n\t\tconst viewportHeight = window.innerHeight;\r\n\r\n\t\t// Get estimated popup dimensions\r\n\t\tconst { width: popupWidth, height: popupHeight } = getEstimatedPopupDimensions();\r\n\r\n\t\t// Viewport margin to ensure tooltip doesn't touch edges\r\n\t\tconst VIEWPORT_MARGIN = 20;\r\n\r\n\t\t// Gap between hotspot and tooltip\r\n\t\tconst TOOLTIP_GAP = 10;\r\n\r\n\t\t// Special gap for tooltips positioned above hotspots (reduced for better visual alignment)\r\n\t\tconst TOOLTIP_GAP_ABOVE = 20;\r\n\r\n\t\t// Handle very small viewports - reduce margins if popup is too large\r\n\t\tconst availableWidth = viewportWidth - (VIEWPORT_MARGIN * 2);\r\n\t\tconst availableHeight = viewportHeight - (VIEWPORT_MARGIN * 2);\r\n\t\tconst effectiveMargin = Math.min(VIEWPORT_MARGIN,\r\n\t\t\tMath.max(5, Math.min((availableWidth - popupWidth) / 2, (availableHeight - popupHeight) / 2))\r\n\t\t);\r\n\r\n\t\t// Calculate hotspot bounds\r\n\t\tconst hotspotRight = hotspotLeft + hotspotSize;\r\n\t\tconst hotspotBottom = hotspotTop + hotspotSize;\r\n\r\n\t\t// Define possible positions in order of preference\r\n\t\tconst positions = [\r\n\t\t\t// Default: bottom-right\r\n\t\t\t{\r\n\t\t\t\tname: 'bottom-right',\r\n\t\t\t\tleft: hotspotRight + TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotBottom + TOOLTIP_GAP\r\n\t\t\t},\r\n\t\t\t// Alternative: bottom-left\r\n\t\t\t{\r\n\t\t\t\tname: 'bottom-left',\r\n\t\t\t\tleft: hotspotLeft - popupWidth - TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotBottom + TOOLTIP_GAP\r\n\t\t\t},\r\n\t\t\t// Alternative: top-right (with 20px gap above hotspot)\r\n\t\t\t{\r\n\t\t\t\tname: 'top-right',\r\n\t\t\t\tleft: hotspotRight + TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotTop - popupHeight - TOOLTIP_GAP_ABOVE\r\n\t\t\t},\r\n\t\t\t// Alternative: top-left (keep original small gap for left corner)\r\n\t\t\t{\r\n\t\t\t\tname: 'top-left',\r\n\t\t\t\tleft: hotspotLeft - popupWidth - TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotTop - popupHeight - TOOLTIP_GAP\r\n\t\t\t},\r\n\t\t\t// Alternative: right-center\r\n\t\t\t{\r\n\t\t\t\tname: 'right-center',\r\n\t\t\t\tleft: hotspotRight + TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotTop + (hotspotSize / 2) - (popupHeight / 2)\r\n\t\t\t},\r\n\t\t\t// Alternative: left-center\r\n\t\t\t{\r\n\t\t\t\tname: 'left-center',\r\n\t\t\t\tleft: hotspotLeft - popupWidth - TOOLTIP_GAP,\r\n\t\t\t\ttop: hotspotTop + (hotspotSize / 2) - (popupHeight / 2)\r\n\t\t\t}\r\n\t\t];\r\n\r\n\t\t// Function to check if a position fits within viewport\r\n\t\tconst isPositionValid = (pos: { left: number; top: number }) => {\r\n\t\t\tconst right = pos.left + popupWidth;\r\n\t\t\tconst bottom = pos.top + popupHeight;\r\n\r\n\t\t\treturn (\r\n\t\t\t\tpos.left >= effectiveMargin &&\r\n\t\t\t\tpos.top >= effectiveMargin &&\r\n\t\t\t\tright <= viewportWidth - effectiveMargin &&\r\n\t\t\t\tbottom <= viewportHeight - effectiveMargin\r\n\t\t\t);\r\n\t\t};\r\n\r\n\t\t// Find the first valid position\r\n\t\tlet selectedPosition = positions.find(pos => isPositionValid(pos));\r\n\r\n\t\t// If no position fits perfectly, use the default and adjust to fit\r\n\t\tif (!selectedPosition) {\r\n\t\t\tselectedPosition = positions[0]; // Default: bottom-right\r\n\r\n\t\t\t// Adjust horizontally if needed\r\n\t\t\tif (selectedPosition.left + popupWidth > viewportWidth - effectiveMargin) {\r\n\t\t\t\tselectedPosition.left = viewportWidth - popupWidth - effectiveMargin;\r\n\t\t\t}\r\n\t\t\tif (selectedPosition.left < effectiveMargin) {\r\n\t\t\t\tselectedPosition.left = effectiveMargin;\r\n\t\t\t}\r\n\r\n\t\t\t// Adjust vertically if needed\r\n\t\t\tif (selectedPosition.top + popupHeight > viewportHeight - effectiveMargin) {\r\n\t\t\t\tselectedPosition.top = viewportHeight - popupHeight - effectiveMargin;\r\n\t\t\t}\r\n\t\t\tif (selectedPosition.top < effectiveMargin) {\r\n\t\t\t\tselectedPosition.top = effectiveMargin;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Debug logging (can be removed in production)\r\n\t\tif (process.env.NODE_ENV === 'development') {\r\n\t\t\tconsole.log('Smart positioning:', {\r\n\t\t\t\thotspotPosition: { left: hotspotLeft, top: hotspotTop },\r\n\t\t\t\thotspotSize: hotspotSize,\r\n\t\t\t\tpopupDimensions: { width: popupWidth, height: popupHeight },\r\n\t\t\t\tviewport: { width: viewportWidth, height: viewportHeight },\r\n\t\t\t\tselectedPosition: selectedPosition.name,\r\n\t\t\t\tfinalPosition: { left: selectedPosition.left, top: selectedPosition.top }\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn {\r\n\t\t\ttop: selectedPosition.top + window.scrollY,\r\n\t\t\tleft: selectedPosition.left + window.scrollX\r\n\t\t};\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY, // Account for scrolling\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tif (typeof window !== undefined) {\r\n\t\t\tconst position = getElementPosition(xpath || \"\");\r\n\t\t\tif (position) {\r\n\t\t\t\tsetPopupPosition(position);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\tconst { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\r\n\r\n\t// Function to check if canvas width has been modified from default\r\n\tconst isCanvasWidthModified = () => {\r\n\t\tconst defaultWidth = \"300\"; // Default width without \"px\"\r\n\t\tconst currentWidth = canvasProperties?.Width?.replace(\"px\", \"\") || defaultWidth;\r\n\t\treturn currentWidth !== defaultWidth;\r\n\t};\r\n\r\n\t// Function to get width styling based on user preferences\r\n\tconst getWidthStyling = () => {\r\n\t\t// Check if user has modified canvas width from default\r\n\t\tconst hasCustomWidth = isCanvasWidthModified();\r\n\r\n\t\tif (hasCustomWidth && canvasProperties?.Width) {\r\n\t\t\t// User-specified width: use exact width, override auto-sizing and max-width\r\n\t\t\tconst userWidth = canvasProperties.Width.includes('px')\r\n\t\t\t\t? canvasProperties.Width\r\n\t\t\t\t: `${canvasProperties.Width}px`;\r\n\t\t\treturn {\r\n\t\t\t\twidth: userWidth,\r\n\t\t\t\tmaxWidth: userWidth, // Override the 300px limit\r\n\t\t\t\tminWidth: 'unset' // Remove any minimum width constraints\r\n\t\t\t};\r\n\t\t} else {\r\n\t\t\t// Default behavior: auto-adjust with 300px maximum, no minimum width\r\n\t\t\treturn {\r\n\t\t\t\twidth: 'auto', // Allow auto-sizing based on content\r\n\t\t\t\tmaxWidth: '300px', // Maximum width constraint\r\n\t\t\t\tminWidth: 'unset' // Remove minimum width constraints for small content\r\n\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\t// Update popup position when content changes (width is now calculated dynamically)\r\n\tuseEffect(() => {\r\n\t\t// Recalculate popup position when content changes since width affects positioning\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep, xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\t// Recalculate popup position when hotspot size changes or content dimensions change\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData, textFieldProperties, imageProperties, customButton]);\r\n\r\n\t// Recalculate popup position on window resize\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tif (xpath && hotspotSize) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\treturn () => window.removeEventListener('resize', handleResize);\r\n\t}, [xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst widthStyling = getWidthStyling();\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\t...widthStyling, // Apply the width styling (width, maxWidth, minWidth)\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\ttop: popupPosition.top + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: popupPosition.left + parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t// \"& .MuiBackdrop-root\": {\r\n\t\t\t\t\t\t\t\t//     position: 'relative !important', // Ensures higher specificity\r\n\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t//...getAnchorAndTransformOrigins,\r\n\t\t\t\t\t\t\t\t\t//top: \"16% !important\",\r\n\t\t\t\t\t\t\t\t\t// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\r\n\t\t\t\t\t\t\t\t\t//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\r\n\t\t\t\t\t\t\t\t\t...getCanvasPosition(canvasProperties?.Position || \"center-center\"),\r\n\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t\t// Add smooth transitions for position changes\r\n\t\t\t\t\t\t\t\t\ttransition: 'top 0.3s ease-out, left 0.3s ease-out',\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: \"400px\" }}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: \"hidden auto\"\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\tpadding: canvasProperties?.Padding || \"10px\",\r\n\t\t\t\t\t\t\t\t\theight: sectionHeight\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxSizing: \"border-box\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordWrap: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\toverflowWrap: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\thyphens: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"100%\"\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t\t\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,GAAG,CAAEC,MAAM,CAAEC,UAAU,CAAEC,cAAc,CAAEC,aAAa,CAAEC,OAAO,CAAiBC,UAAU,KAAQ,eAAe,CAE1H,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE;AACA,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBA6GrD,KAAM,CAAAC,cAAoC,CAAGC,IAAA,EA8BvC,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IA9BwC,CAC1CC,QAAQ,CACRC,SAAS,CACTC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,QAAQ,CACRC,WAAW,CACXC,UAAU,CACVC,eAAe,CACfC,QAAQ,CACRC,mBAAmB,CACnBC,eAAe,CACfC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,WAAW,CACXC,oBAAoB,CACpBC,oBAAoB,CACpBC,YAAY,CACZC,cAAc,CACdC,iBAAiB,CACjBC,kBAAkB,CAClBC,kBAAkB,CAClBC,kBAAkB,CACnBC,kBAEH,CAAC,CAAAvC,IAAA,CACA,KAAM,CACLwC,cAAc,CACdC,gBAAgB,CAChBC,oBAAoB,CACpBC,eAAe,CACfC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,gBAAgB,CAChBC,oBAAoB,CACpBC,oBAAoB,CACpBC,cAAc,CACdC,aACD,CAAC,CAAG9D,cAAc,CAAE+D,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG3E,QAAQ,CAAqB,IAAI,CAAC,CAC5E;AACA;AACA,KAAM,CAAC4E,aAAa,CAAEC,gBAAgB,CAAC,CAAG7E,QAAQ,CAAuC,IAAI,CAAC,CAC9F,KAAM,CAAC8E,WAAW,CAAEC,cAAc,CAAC,CAAG/E,QAAQ,CAAS,EAAE,CAAC,CAAE;AAC5D,KAAM,CAAAgF,UAAU,CAAG/E,MAAM,CAAiB,IAAI,CAAC,CAC/C,KAAM,CAAAgF,kBAAkB,CAAGhF,MAAM,CAAiB,IAAI,CAAC,CACvD,GAAI,CAAAiF,OAAY,CAChB,KAAM,CAAAC,iBAAiB,CAAIC,KAAa,EAAyB,CAChE,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,CAAEE,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CAClG,KAAM,CAAAC,IAAI,CAAGL,MAAM,CAACM,eAAe,CACnC,GAAID,IAAI,WAAY,CAAAE,WAAW,CAAE,CAChC,MAAO,CAAAF,IAAI,CACZ,CAAC,IAAM,IAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,aAAa,CAAE,CAC/B,MAAO,CAAAH,IAAI,CAACG,aAAa,CAAE;AAC5B,CAAC,IAAM,CACN,MAAO,KAAI,CACZ,CACD,CAAC,CACD,GAAI,CAAAT,KAAU,CACd,GAAI/B,cAAc,CAAE+B,KAAK,CAAG/B,cAAc,SAAdA,cAAc,kBAAAjC,qBAAA,CAAdiC,cAAc,CAAEyC,SAAS,UAAA1E,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,iBAA9BA,sBAAA,CAAgC0E,WAAW,CACvE,KAAM,CAAAC,kBAAkB,CAAIZ,KAAyB,EAAK,CACzD,KAAM,CAAAa,OAAO,CAAGd,iBAAiB,CAACC,KAAK,EAAI,EAAE,CAAC,CAC9C,GAAIa,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,MAAO,CACNC,GAAG,CAAEF,IAAI,CAACE,GAAG,CAAE;AACfC,IAAI,CAAEH,IAAI,CAACG,IAAM;AAClB,CAAC,CACF,CACA,MAAO,KAAI,CACZ,CAAC,CACC;AACA,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGvG,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAwG,YAAY,CAAGvG,MAAM,CAAM,IAAI,CAAC,CACxC;AACA,KAAM,CAAAwG,2BAA2B,CAAGA,CAAA,GAAM,CACzC;AACA,GAAIzB,UAAU,CAAC0B,OAAO,CAAE,CACvB,KAAM,CAAAC,WAAW,CAAG3B,UAAU,CAAC0B,OAAO,CAACP,qBAAqB,CAAC,CAAC,CAC9D,MAAO,CACNS,KAAK,CAAEC,IAAI,CAACC,GAAG,CAACH,WAAW,CAACC,KAAK,CAAE,EAAE,CAAC,CAAE;AACxCG,MAAM,CAAEF,IAAI,CAACC,GAAG,CAACH,WAAW,CAACI,MAAM,CAAG,GAAG,CAAE,GAAG,CAAE;AACjD,CAAC,CACF,CAEA;AACA,KAAM,CAAAC,YAAY,CAAGC,eAAe,CAAC,CAAC,CACtC,GAAI,CAAAC,cAAc,CAAG,GAAG,CAAE;AAE1B,GAAIF,YAAY,CAACJ,KAAK,GAAK,MAAM,CAAE,CAClC;AACA,KAAM,CAAAO,QAAQ,CAAGC,QAAQ,CAACJ,YAAY,CAACG,QAAQ,CAAC,EAAI,GAAG,CACvD;AACAD,cAAc,CAAGL,IAAI,CAACQ,GAAG,CAACF,QAAQ,CAAE,GAAG,CAAC,CAAE;AAC3C,CAAC,IAAM,CACN;AACAD,cAAc,CAAGE,QAAQ,CAACJ,YAAY,CAACJ,KAAK,CAAC,EAAI,GAAG,CACrD,CAEA,KAAM,CAAAU,eAAe,CAAG,GAAG,CAAE;AAE7B,MAAO,CACNV,KAAK,CAAEM,cAAc,CACrBH,MAAM,CAAEO,eACT,CAAC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,sBAAsB,CAAGA,CAACC,WAAoB,CAAE1C,WAAmB,CAAE2C,OAAe,CAAEC,OAAe,GAAK,CAC/G,KAAM,CAAAC,WAAW,CAAGH,WAAW,CAACI,CAAC,CAAGH,OAAO,CAC3C,KAAM,CAAAI,UAAU,CAAGL,WAAW,CAACM,CAAC,CAAGJ,OAAO,CAE1C;AACA,KAAM,CAAAK,aAAa,CAAGC,MAAM,CAACC,UAAU,CACvC,KAAM,CAAAC,cAAc,CAAGF,MAAM,CAACG,WAAW,CAEzC;AACA,KAAM,CAAEvB,KAAK,CAAEwB,UAAU,CAAErB,MAAM,CAAEsB,WAAY,CAAC,CAAG5B,2BAA2B,CAAC,CAAC,CAEhF;AACA,KAAM,CAAA6B,eAAe,CAAG,EAAE,CAE1B;AACA,KAAM,CAAAC,WAAW,CAAG,EAAE,CAEtB;AACA,KAAM,CAAAC,iBAAiB,CAAG,EAAE,CAE5B;AACA,KAAM,CAAAC,cAAc,CAAGV,aAAa,CAAIO,eAAe,CAAG,CAAE,CAC5D,KAAM,CAAAI,eAAe,CAAGR,cAAc,CAAII,eAAe,CAAG,CAAE,CAC9D,KAAM,CAAAK,eAAe,CAAG9B,IAAI,CAACQ,GAAG,CAACiB,eAAe,CAC/CzB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACQ,GAAG,CAAC,CAACoB,cAAc,CAAGL,UAAU,EAAI,CAAC,CAAE,CAACM,eAAe,CAAGL,WAAW,EAAI,CAAC,CAAC,CAC7F,CAAC,CAED;AACA,KAAM,CAAAO,YAAY,CAAGjB,WAAW,CAAG7C,WAAW,CAC9C,KAAM,CAAA+D,aAAa,CAAGhB,UAAU,CAAG/C,WAAW,CAE9C;AACA,KAAM,CAAAgE,SAAS,CAAG,CACjB;AACA,CACCC,IAAI,CAAE,cAAc,CACpB1C,IAAI,CAAEuC,YAAY,CAAGL,WAAW,CAChCnC,GAAG,CAAEyC,aAAa,CAAGN,WACtB,CAAC,CACD;AACA,CACCQ,IAAI,CAAE,aAAa,CACnB1C,IAAI,CAAEsB,WAAW,CAAGS,UAAU,CAAGG,WAAW,CAC5CnC,GAAG,CAAEyC,aAAa,CAAGN,WACtB,CAAC,CACD;AACA,CACCQ,IAAI,CAAE,WAAW,CACjB1C,IAAI,CAAEuC,YAAY,CAAGL,WAAW,CAChCnC,GAAG,CAAEyB,UAAU,CAAGQ,WAAW,CAAGG,iBACjC,CAAC,CACD;AACA,CACCO,IAAI,CAAE,UAAU,CAChB1C,IAAI,CAAEsB,WAAW,CAAGS,UAAU,CAAGG,WAAW,CAC5CnC,GAAG,CAAEyB,UAAU,CAAGQ,WAAW,CAAGE,WACjC,CAAC,CACD;AACA,CACCQ,IAAI,CAAE,cAAc,CACpB1C,IAAI,CAAEuC,YAAY,CAAGL,WAAW,CAChCnC,GAAG,CAAEyB,UAAU,CAAI/C,WAAW,CAAG,CAAE,CAAIuD,WAAW,CAAG,CACtD,CAAC,CACD;AACA,CACCU,IAAI,CAAE,aAAa,CACnB1C,IAAI,CAAEsB,WAAW,CAAGS,UAAU,CAAGG,WAAW,CAC5CnC,GAAG,CAAEyB,UAAU,CAAI/C,WAAW,CAAG,CAAE,CAAIuD,WAAW,CAAG,CACtD,CAAC,CACD,CAED;AACA,KAAM,CAAAW,eAAe,CAAIC,GAAkC,EAAK,CAC/D,KAAM,CAAAC,KAAK,CAAGD,GAAG,CAAC5C,IAAI,CAAG+B,UAAU,CACnC,KAAM,CAAAe,MAAM,CAAGF,GAAG,CAAC7C,GAAG,CAAGiC,WAAW,CAEpC,MACC,CAAAY,GAAG,CAAC5C,IAAI,EAAIsC,eAAe,EAC3BM,GAAG,CAAC7C,GAAG,EAAIuC,eAAe,EAC1BO,KAAK,EAAInB,aAAa,CAAGY,eAAe,EACxCQ,MAAM,EAAIjB,cAAc,CAAGS,eAAe,CAE5C,CAAC,CAED;AACA,GAAI,CAAAS,gBAAgB,CAAGN,SAAS,CAACO,IAAI,CAACJ,GAAG,EAAID,eAAe,CAACC,GAAG,CAAC,CAAC,CAElE;AACA,GAAI,CAACG,gBAAgB,CAAE,CACtBA,gBAAgB,CAAGN,SAAS,CAAC,CAAC,CAAC,CAAE;AAEjC;AACA,GAAIM,gBAAgB,CAAC/C,IAAI,CAAG+B,UAAU,CAAGL,aAAa,CAAGY,eAAe,CAAE,CACzES,gBAAgB,CAAC/C,IAAI,CAAG0B,aAAa,CAAGK,UAAU,CAAGO,eAAe,CACrE,CACA,GAAIS,gBAAgB,CAAC/C,IAAI,CAAGsC,eAAe,CAAE,CAC5CS,gBAAgB,CAAC/C,IAAI,CAAGsC,eAAe,CACxC,CAEA;AACA,GAAIS,gBAAgB,CAAChD,GAAG,CAAGiC,WAAW,CAAGH,cAAc,CAAGS,eAAe,CAAE,CAC1ES,gBAAgB,CAAChD,GAAG,CAAG8B,cAAc,CAAGG,WAAW,CAAGM,eAAe,CACtE,CACA,GAAIS,gBAAgB,CAAChD,GAAG,CAAGuC,eAAe,CAAE,CAC3CS,gBAAgB,CAAChD,GAAG,CAAGuC,eAAe,CACvC,CACD,CAEA;AACA,GAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,CAAE,CAC3CC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAE,CACjCC,eAAe,CAAE,CAAEtD,IAAI,CAAEsB,WAAW,CAAEvB,GAAG,CAAEyB,UAAW,CAAC,CACvD/C,WAAW,CAAEA,WAAW,CACxB8E,eAAe,CAAE,CAAEhD,KAAK,CAAEwB,UAAU,CAAErB,MAAM,CAAEsB,WAAY,CAAC,CAC3DwB,QAAQ,CAAE,CAAEjD,KAAK,CAAEmB,aAAa,CAAEhB,MAAM,CAAEmB,cAAe,CAAC,CAC1DkB,gBAAgB,CAAEA,gBAAgB,CAACL,IAAI,CACvCe,aAAa,CAAE,CAAEzD,IAAI,CAAE+C,gBAAgB,CAAC/C,IAAI,CAAED,GAAG,CAAEgD,gBAAgB,CAAChD,GAAI,CACzE,CAAC,CAAC,CACH,CAEA,MAAO,CACNA,GAAG,CAAEgD,gBAAgB,CAAChD,GAAG,CAAG4B,MAAM,CAAC+B,OAAO,CAC1C1D,IAAI,CAAE+C,gBAAgB,CAAC/C,IAAI,CAAG2B,MAAM,CAACgC,OACtC,CAAC,CACF,CAAC,CACDjK,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkG,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIa,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CtB,gBAAgB,CAAC,CAChBuB,GAAG,CAAEF,IAAI,CAACE,GAAG,CAAG4B,MAAM,CAAC+B,OAAO,CAAE;AAChC1D,IAAI,CAAEH,IAAI,CAACG,IAAI,CAAG2B,MAAM,CAACgC,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAAC5E,KAAK,CAAC,CAAC,CACXrF,SAAS,CAAC,IAAM,CACf,GAAI,MAAO,CAAAiI,MAAM,GAAKiC,SAAS,CAAE,CAChC,KAAM,CAAAC,QAAQ,CAAGlE,kBAAkB,CAACZ,KAAK,EAAI,EAAE,CAAC,CAChD,GAAI8E,QAAQ,CAAE,CACbrF,gBAAgB,CAACqF,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAAC9E,KAAK,CAAC,CAAC,CACXrF,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkG,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC;AACA,GAAIa,OAAO,CAAE,CACb,CACD,CAAC,CAAE,CAAC5C,cAAc,CAAC,CAAC,CAEpBtD,SAAS,CAAC,IAAM,KAAAoK,UAAA,CACf,KAAM,CAAAlE,OAAO,CAAGd,iBAAiB,CAACnD,SAAS,SAATA,SAAS,kBAAAmI,UAAA,CAATnI,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAA2H,UAAA,iBAA5BA,UAAA,CAA8BpE,WAAW,CAAC,CAC5EpB,gBAAgB,CAACsB,OAAO,CAAC,CACzB,GAAIA,OAAO,CAAE,CACZA,OAAO,CAACmE,KAAK,CAACC,eAAe,CAAG,gBAAgB,CAEhD;AACA,KAAM,CAAAnE,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CtB,gBAAgB,CAAC,CAChBuB,GAAG,CAAEF,IAAI,CAACE,GAAG,CAAG4B,MAAM,CAAC+B,OAAO,CAC9B1D,IAAI,CAAEH,IAAI,CAACG,IAAI,CAAG2B,MAAM,CAACgC,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAAChI,SAAS,CAAEQ,WAAW,CAAC,CAAC,CAE5B;AACA;AACA,KAAM,EAAG8H,eAAe,CAAC,CAAGtK,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAAuK,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI3G,gBAAgB,GAAK,MAAM,CAAE,CAChC,GAAIpB,WAAW,CAAGC,UAAU,CAAE,CAC7BkB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/BF,UAAU,CAAC,CAAC,CACZkI,eAAe,CAAChI,WAAW,CAAGC,UAAU,CAAC,CAC1C,CACD,CAAC,IAAM,CACNkB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/B,KAAM,CAAAiI,eAAe,CAAGnF,QAAQ,CAACoF,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAACL,KAAK,CAACO,OAAO,CAAG,MAAM,CACtCF,eAAe,CAACG,MAAM,CAAC,CAAC,CACzB,CACD,CACD,CAAC,CAED,KAAM,CAAAJ,eAAe,CAAIK,qBAA8B,EAAK,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAC3D,MAAO,CAAAV,qBAAqB,cAC3BhK,IAAA,CAACK,cAAc,EACduC,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCH,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCzB,QAAQ,CAAEA,QAAS,CACnBsB,cAAc,CAAEA,cAAe,CAC/BrB,SAAS,CAAEA,SAAU,CACrBI,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAEmJ,cAAe,CAC3BlJ,UAAU,CAAEiI,cAAe,CAC3BtI,KAAK,CAAEA,KAAM,CACbC,IAAI,CAAEA,IAAK,CACXC,QAAQ,CAAEA,QAAS,CACnBK,WAAW,CAAEA,WAAW,CAAG,CAAE,CAC7BC,UAAU,CAAEA,UAAW,CACvBC,eAAe,CAAEA,eAAgB,CACjCC,QAAQ,CAAEA,QAAS,CACnBC,mBAAmB,CAAES,cAAc,SAAdA,cAAc,kBAAAyH,sBAAA,CAAdzH,cAAc,CAAEyC,SAAS,UAAAgF,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4BtI,WAAW,CAAC,UAAAuI,sBAAA,iBAAxCA,sBAAA,CAA0CU,mBAAoB,CACnF5I,eAAe,CAAEQ,cAAc,SAAdA,cAAc,kBAAA2H,sBAAA,CAAd3H,cAAc,CAAEyC,SAAS,UAAAkF,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4BxI,WAAW,CAAC,UAAAyI,sBAAA,iBAAxCA,sBAAA,CAA0CS,eAAgB,CAC3E5I,YAAY,CACX,CAAAO,cAAc,SAAdA,cAAc,kBAAA6H,sBAAA,CAAd7H,cAAc,CAAEyC,SAAS,UAAAoF,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B1I,WAAW,CAAC,UAAA2I,sBAAA,kBAAAC,sBAAA,CAAxCD,sBAAA,CAA0CQ,aAAa,UAAAP,sBAAA,kBAAAC,uBAAA,CAAvDD,sBAAA,CAAyDQ,GAAG,CAAEC,OAAY,EACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,GAAM,CAC3C,GAAGA,MAAM,CACTC,WAAW,CAAEH,OAAO,CAACI,EAAI;AAC1B,CAAC,CAAC,CACH,CAAC,UAAAZ,uBAAA,iBALDA,uBAAA,CAKGa,MAAM,CAAC,CAACC,GAAmB,CAAEC,IAAS,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EACvE,CACDrJ,eAAe,CAAEA,eAAgB,CACjCC,gBAAgB,CAAEA,gBAAiB,CACnCC,WAAW,CAAEA,WAAY,CACzBG,YAAY,CAAEA,YAAa,CAC3BE,iBAAiB,CAAE,CAAAD,cAAc,SAAdA,cAAc,kBAAAiI,uBAAA,CAAdjI,cAAc,CAAEyC,SAAS,UAAAwF,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B9I,WAAW,CAAG,CAAC,CAAC,UAAA+I,uBAAA,iBAA5CA,uBAAA,CAA8Ce,OAAO,GAAI,CAAC,CAAE,CAC/E,CAAC,CACC,IAAI,CACT,CAAC,CAED,KAAM,CAAAd,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAIhJ,WAAW,CAAG,CAAC,CAAE,CACpBmB,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/BH,UAAU,CAAC,CAAC,CACb,CACD,CAAC,CACDtC,SAAS,CAAC,IAAM,CACf,GAAIqD,YAAY,CAAE,CACjBkH,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACNA,eAAe,CAAC,KAAK,CAAC,CACvB,CACD,CAAC,CAAE,CAAClH,YAAY,CAAC,CAAC,CAClB;AACA,KAAM,CAAAmJ,4BAA4B,CACjCrC,QAAgB,EACqD,CACrE,OAAQA,QAAQ,EACf,IAAK,UAAU,CACd,MAAO,CACNsC,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,MAAO,CAAC,CACrDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,WAAW,CACf,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACtDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CACzD,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,YAAY,CAChB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAC,CACvDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,QACC,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACH,CACD,CAAC,CAED,KAAM,CAAEF,YAAY,CAAEG,eAAgB,CAAC,CAAGJ,4BAA4B,CAAC,CAAAvJ,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE4J,QAAQ,GAAI,eAAe,CAAC,CAErH,KAAM,CAAAC,SAAS,CAAG,CACjBC,UAAU,CAAElK,mBAAmB,SAAnBA,mBAAmB,YAAAtB,qBAAA,CAAnBsB,mBAAmB,CAAEmK,cAAc,UAAAzL,qBAAA,WAAnCA,qBAAA,CAAqC0L,IAAI,CAAG,MAAM,CAAG,QAAQ,CACzEC,SAAS,CAAErK,mBAAmB,SAAnBA,mBAAmB,YAAArB,sBAAA,CAAnBqB,mBAAmB,CAAEmK,cAAc,UAAAxL,sBAAA,WAAnCA,sBAAA,CAAqC2L,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAC5EC,KAAK,CAAE,CAAAvK,mBAAmB,SAAnBA,mBAAmB,kBAAApB,sBAAA,CAAnBoB,mBAAmB,CAAEmK,cAAc,UAAAvL,sBAAA,iBAAnCA,sBAAA,CAAqC4L,SAAS,GAAI,SAAS,CAClEC,SAAS,CAAE,CAAAzK,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAE0K,SAAS,GAAI,MAC9C,CAAC,CAED;AAEA,KAAM,CAAAC,iBAAiB,CAAIC,OAAe,EAAK,CAC9C;AACA,MAAO,CACNC,MAAM,CAAED,OAAO,CAACE,OAAO,CAAC,qCAAqC,CAAE,CAACC,MAAM,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CACtF,MAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE,CAC1C,CAAC,CACF,CAAC,CACF,CAAC,CAID;AACA,KAAM,CAAAC,qBAAqB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CACnC,KAAM,CAAAC,YAAY,CAAG,KAAK,CAAE;AAC5B,KAAM,CAAAC,YAAY,CAAG,CAAAlL,gBAAgB,SAAhBA,gBAAgB,kBAAAgL,qBAAA,CAAhBhL,gBAAgB,CAAEmL,KAAK,UAAAH,qBAAA,iBAAvBA,qBAAA,CAAyBN,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,GAAIO,YAAY,CAC/E,MAAO,CAAAC,YAAY,GAAKD,YAAY,CACrC,CAAC,CAED;AACA,KAAM,CAAAhH,eAAe,CAAGA,CAAA,GAAM,CAC7B;AACA,KAAM,CAAAmH,cAAc,CAAGL,qBAAqB,CAAC,CAAC,CAE9C,GAAIK,cAAc,EAAIpL,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEmL,KAAK,CAAE,CAC9C;AACA,KAAM,CAAAE,SAAS,CAAGrL,gBAAgB,CAACmL,KAAK,CAACG,QAAQ,CAAC,IAAI,CAAC,CACpDtL,gBAAgB,CAACmL,KAAK,CACtB,GAAGnL,gBAAgB,CAACmL,KAAK,IAAI,CAChC,MAAO,CACNvH,KAAK,CAAEyH,SAAS,CAChBlH,QAAQ,CAAEkH,SAAS,CAAE;AACrBE,QAAQ,CAAE,OAAQ;AACnB,CAAC,CACF,CAAC,IAAM,CACN;AACA,MAAO,CACN3H,KAAK,CAAE,MAAM,CAAE;AACfO,QAAQ,CAAE,OAAO,CAAE;AACnBoH,QAAQ,CAAE,OAAQ;AACnB,CAAC,CACF,CACD,CAAC,CAED;AACAxO,SAAS,CAAC,IAAM,CACf;AACA,GAAIqF,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAmB,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIa,OAAO,CAAE,KAAAuI,qBAAA,CACZ,KAAM,CAAAtI,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAsI,eAAe,EAAAD,qBAAA,CAAG3K,oBAAoB,CAAC,CAAC,CAAC,UAAA2K,qBAAA,iBAAvBA,qBAAA,CAAyBE,QAAQ,CACzD,KAAM,CAAAjH,OAAO,CAAGkH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAlH,OAAO,CAAGiH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAC,QAAQ,CAAGvH,sBAAsB,CAACrB,IAAI,CAAEpB,WAAW,CAAE2C,OAAO,CAAEC,OAAO,CAAC,CAC5E7C,gBAAgB,CAACiK,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAAClM,mBAAmB,CAAEC,eAAe,CAAEC,YAAY,CAAEN,WAAW,CAAE4C,KAAK,CAAEN,WAAW,CAAEjB,oBAAoB,CAAC,CAAC,CAE/G;AACA9D,SAAS,CAAC,IAAM,CACf,GAAIqF,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAmB,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIa,OAAO,CAAE,KAAA8I,sBAAA,CACZ,KAAM,CAAA7I,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAsI,eAAe,EAAAM,sBAAA,CAAGlL,oBAAoB,CAAC,CAAC,CAAC,UAAAkL,sBAAA,iBAAvBA,sBAAA,CAAyBL,QAAQ,CACzD,KAAM,CAAAjH,OAAO,CAAGkH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAlH,OAAO,CAAGiH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAC,QAAQ,CAAGvH,sBAAsB,CAACrB,IAAI,CAAEpB,WAAW,CAAE2C,OAAO,CAAEC,OAAO,CAAC,CAC5E7C,gBAAgB,CAACiK,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAAChK,WAAW,CAAEM,KAAK,CAAEvB,oBAAoB,CAAEjB,mBAAmB,CAAEC,eAAe,CAAEC,YAAY,CAAC,CAAC,CAElG;AACA/C,SAAS,CAAC,IAAM,CACf,KAAM,CAAAiP,YAAY,CAAGA,CAAA,GAAM,CAC1B,GAAI5J,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAmB,OAAO,CAAGd,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIa,OAAO,CAAE,KAAAgJ,sBAAA,CACZ,KAAM,CAAA/I,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAsI,eAAe,EAAAQ,sBAAA,CAAGpL,oBAAoB,CAAC,CAAC,CAAC,UAAAoL,sBAAA,iBAAvBA,sBAAA,CAAyBP,QAAQ,CACzD,KAAM,CAAAjH,OAAO,CAAGkH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAlH,OAAO,CAAGiH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAC,QAAQ,CAAGvH,sBAAsB,CAACrB,IAAI,CAAEpB,WAAW,CAAE2C,OAAO,CAAEC,OAAO,CAAC,CAC5E7C,gBAAgB,CAACiK,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAED9G,MAAM,CAACkH,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAC/C,MAAO,IAAMhH,MAAM,CAACmH,mBAAmB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAChE,CAAC,CAAE,CAAC5J,KAAK,CAAEN,WAAW,CAAEjB,oBAAoB,CAAC,CAAC,CAE9C,KAAM,CAAAuL,cAAc,CAAGtM,YAAY,CAACoJ,MAAM,CAAC,CAACC,GAAQ,CAAEJ,MAAW,GAAK,CACrE,KAAM,CAAAsD,WAAW,CAAGtD,MAAM,CAACC,WAAW,EAAI,SAAS,CAAE;AACrD,GAAI,CAACG,GAAG,CAACkD,WAAW,CAAC,CAAE,CACtBlD,GAAG,CAACkD,WAAW,CAAC,CAAG,EAAE,CACtB,CACAlD,GAAG,CAACkD,WAAW,CAAC,CAACC,IAAI,CAACvD,MAAM,CAAC,CAC7B,MAAO,CAAAI,GAAG,CACX,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAnF,YAAY,CAAGC,eAAe,CAAC,CAAC,CACtC,KAAM,CAAAsI,WAAW,CAAG,CACnBrF,QAAQ,CAAE,CAAAlH,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE4J,QAAQ,GAAI,eAAe,CACvD4C,YAAY,CAAE,CAAAxM,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEyM,MAAM,GAAI,KAAK,CAC/CC,WAAW,CAAE,CAAA1M,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE2M,UAAU,GAAI,KAAK,CAClDC,WAAW,CAAE,CAAA5M,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE6M,WAAW,GAAI,OAAO,CACrDC,WAAW,CAAE,OAAO,CACpBzF,eAAe,CAAE,CAAArH,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE+M,eAAe,GAAI,OAAO,CAC7D,GAAG/I,YAAc;AAClB,CAAC,CACD,KAAM,CAAAgJ,aAAa,CAAG,EAAAvO,gBAAA,CAAAoB,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAAf,gBAAA,kBAAAC,qBAAA,CAAhCD,gBAAA,CAAkCwO,WAAW,UAAAvO,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgDc,WAAW,CAAG,CAAC,CAAC,UAAAb,sBAAA,iBAAhEA,sBAAA,CAAkEuO,aAAa,GAAI,MAAM,CAC/G,KAAM,CAAAC,kBAAkB,CAAIC,MAAW,EAAK,CAC3C,GAAIA,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,EAAID,MAAM,CAACC,MAAM,GAAK,SAAS,CAAE,CAC5F,KAAM,CAAAC,SAAS,CAAGF,MAAM,CAACG,SAAS,CAClC,GAAIH,MAAM,CAACI,WAAW,GAAK,UAAU,CAAE,CACtC;AACAxI,MAAM,CAACyI,QAAQ,CAACC,IAAI,CAAGJ,SAAS,CACjC,CAAC,IAAM,CACN;AACAtI,MAAM,CAAC2I,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAC,IAAM,CACN,GACCF,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACI,WAAW,EAAI,UAAU,EAChCJ,MAAM,CAACI,WAAW,EAAI,UAAU,CAC/B,CACDhF,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACN4E,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACI,WAAW,EAAI,MAAM,EAC5BJ,MAAM,CAACI,WAAW,EAAI,MAAM,CAC3B,CACDjG,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACN6F,MAAM,CAACC,MAAM,EAAI,SAAS,EAC1BD,MAAM,CAACI,WAAW,EAAI,SAAS,CAC9B,KAAAI,uBAAA,CAAAC,uBAAA,CACD;AACAlN,cAAc,CAAC,CAAC,CAAC,CACjB;AACA,GAAIN,cAAc,SAAdA,cAAc,YAAAuN,uBAAA,CAAdvN,cAAc,CAAEyC,SAAS,UAAA8K,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,WAA9BA,uBAAA,CAAgC9K,WAAW,CAAE,CAChD,KAAM,CAAA+K,gBAAgB,CAAG3L,iBAAiB,CAAC9B,cAAc,CAACyC,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CACnF,GAAI+K,gBAAgB,CAAE,CACrBA,gBAAgB,CAACC,cAAc,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CACxD,CACD,CACD,CACD,CACA1G,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CACDvK,SAAS,CAAC,IAAM,KAAAkR,WAAA,CAAAC,mBAAA,CACf,GAAIlP,SAAS,SAATA,SAAS,YAAAiP,WAAA,CAATjP,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAAyO,WAAA,YAAAC,mBAAA,CAA5BD,WAAA,CAA8B3E,OAAO,UAAA4E,mBAAA,WAArCA,mBAAA,CAAuCC,aAAa,CAAE,CACzD;AACAjN,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAAE,CAAClC,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,CAAEA,WAAW,CAAE0B,cAAc,CAAC,CAAC,CAE/D;AACAnE,SAAS,CAAC,IAAM,CACf,GAAI0D,kBAAkB,CAAE,KAAA2N,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAAhD,eAAe,CAAGnK,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAuN,sBAAA,CAApBvN,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA4O,sBAAA,WAAvCA,sBAAA,CAAyC1C,QAAQ,CAC5G7K,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACkM,QAAQ,EAAA2C,sBAAA,CAC9CxN,oBAAoB,CAAC,CAAC,CAAC,UAAAwN,sBAAA,iBAAvBA,sBAAA,CAAyB3C,QAAQ,CACpC,KAAM,CAAAgD,WAAW,CAAGpN,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAAiO,uBAAA,CAAdjO,cAAc,CAAEyC,SAAS,UAAAwL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B9O,WAAW,CAAG,CAAC,CAAC,UAAA+O,uBAAA,iBAA5CA,uBAAA,CAA8CjF,OAAO,CACrDjJ,cAAc,SAAdA,cAAc,kBAAAmO,uBAAA,CAAdnO,cAAc,CAAEyC,SAAS,UAAA0L,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCnF,OAAO,CAE1C;AACA;AACA,GAAImC,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAE0C,aAAa,CAAE,CACnC;AACAjN,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACT,kBAAkB,CAAEI,oBAAoB,CAAC,CAAC,CAE9C;AACA9D,SAAS,CAAC,IAAM,CACf,GAAI2D,kBAAkB,CAAE,KAAAiO,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAAvD,eAAe,CAAGnK,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAA8N,sBAAA,CAApB9N,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAAmP,sBAAA,WAAvCA,sBAAA,CAAyCjD,QAAQ,CAC5G7K,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACkM,QAAQ,EAAAkD,sBAAA,CAC9C/N,oBAAoB,CAAC,CAAC,CAAC,UAAA+N,sBAAA,iBAAvBA,sBAAA,CAAyBlD,QAAQ,CACpC,KAAM,CAAAgD,WAAW,CAAGpN,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAAwO,uBAAA,CAAdxO,cAAc,CAAEyC,SAAS,UAAA+L,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BrP,WAAW,CAAG,CAAC,CAAC,UAAAsP,uBAAA,iBAA5CA,uBAAA,CAA8CxF,OAAO,CACrDjJ,cAAc,SAAdA,cAAc,kBAAA0O,uBAAA,CAAd1O,cAAc,CAAEyC,SAAS,UAAAiM,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgC1F,OAAO,CAE1C;AACA,GAAImC,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAE0C,aAAa,CAAE,CACnC;AACAjN,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACR,kBAAkB,CAAEG,oBAAoB,CAAC,CAAC,CAE9C;AACA9D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkS,iBAAiB,CAAIC,CAAa,EAAK,CAC5C,KAAM,CAAAC,cAAc,CAAG7M,QAAQ,CAACoF,cAAc,CAAC,cAAc,CAAC,CAE9D;AACA,GAAIyH,cAAc,EAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,CAAE,CAChE,OACD,CAEA;AACA;AACD,CAAC,CAED/M,QAAQ,CAAC4J,gBAAgB,CAAC,OAAO,CAAE+C,iBAAiB,CAAC,CAErD,MAAO,IAAM,CACZ3M,QAAQ,CAAC6J,mBAAmB,CAAC,OAAO,CAAE8C,iBAAiB,CAAC,CACzD,CAAC,CACF,CAAC,CAAE,CAACpO,oBAAoB,CAAC,CAAC,CAC1B;AACA9D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAuS,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAItN,UAAU,CAAC0B,OAAO,CAAE,CACvB;AACA1B,UAAU,CAAC0B,OAAO,CAAC0D,KAAK,CAACrD,MAAM,CAAG,MAAM,CACxC,KAAM,CAAAwL,aAAa,CAAGvN,UAAU,CAAC0B,OAAO,CAAC8L,YAAY,CACrD,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGH,aAAa,CAAGE,eAAe,CAGpDlM,iBAAiB,CAACmM,YAAY,CAAC,CAE/B;AACA,GAAIlM,YAAY,CAACE,OAAO,CAAE,CACzB;AACA,GAAIF,YAAY,CAACE,OAAO,CAACiM,YAAY,CAAE,CACtCnM,YAAY,CAACE,OAAO,CAACiM,YAAY,CAAC,CAAC,CACpC,CACA;AACAC,UAAU,CAAC,IAAM,CAChB,GAAIpM,YAAY,CAACE,OAAO,EAAIF,YAAY,CAACE,OAAO,CAACiM,YAAY,CAAE,CAC9DnM,YAAY,CAACE,OAAO,CAACiM,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDL,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAO,QAAQ,CAAG,CAChBD,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CACjCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClCM,UAAU,CAACN,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAQ,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAI/N,UAAU,CAAC0B,OAAO,EAAIsB,MAAM,CAACgL,cAAc,CAAE,CAChDF,cAAc,CAAG,GAAI,CAAAE,cAAc,CAAC,IAAM,CACzCJ,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFQ,cAAc,CAACG,OAAO,CAACjO,UAAU,CAAC0B,OAAO,CAAC,CAC3C,CAGA,GAAI1B,UAAU,CAAC0B,OAAO,EAAIsB,MAAM,CAACkL,gBAAgB,CAAE,CAClDH,gBAAgB,CAAG,GAAI,CAAAG,gBAAgB,CAAC,IAAM,CAC7CN,UAAU,CAACN,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFS,gBAAgB,CAACE,OAAO,CAACjO,UAAU,CAAC0B,OAAO,CAAE,CAC5CyM,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZT,QAAQ,CAACU,OAAO,CAACC,YAAY,CAAC,CAC9B,GAAIV,cAAc,CAAE,CACnBA,cAAc,CAACW,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIV,gBAAgB,CAAE,CACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAACjR,WAAW,CAAC,CAAC,CACjB;AACA;AAEA,QAAS,CAAAkR,YAAYA,CAACC,SAAiB,CAAE,CACxC,OAAQA,SAAS,EAChB,IAAK,OAAO,CACX,MAAO,YAAY,CACpB,IAAK,KAAK,CACT,MAAO,UAAU,CAClB,IAAK,QAAQ,CACb,QACC,MAAO,QAAQ,CACjB,CACD,CACA,KAAM,CAAAC,iBAAiB,CAAG,QAAAA,CAAA,CAAwC,IAAvC,CAAA1J,QAAgB,CAAA2J,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAA5J,SAAA,CAAA4J,SAAA,IAAG,eAAe,CAC5D,OAAQ3J,QAAQ,EACf,IAAK,aAAa,CACjB,MAAO,CAAE9D,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,cAAc,CAClB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,aAAa,CACjB,MAAO,CAAEA,GAAG,CAAEjE,QAAQ,GAAK,EAAE,CAAG,gBAAgB,CAAG,gBAAiB,CAAC,CACtE,IAAK,cAAc,CAClB,MAAO,CAAEiE,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,UAAU,CACd,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,WAAW,CACf,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,YAAY,CAChB,MAAO,CAAEA,GAAG,CAAE,eAAgB,CAAC,CAChC,QACC,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CAClC,CACD,CAAC,CAEA;AACD,KAAM,CAAA2N,kBAAkB,CAAGA,CAACC,QAAgB,CAAEvF,eAAoB,CAAEiD,WAAgB,GAAK,CACxF,GAAIpN,oBAAoB,GAAK,SAAS,CAAE,CACvC;AACA,OAAQ0P,QAAQ,EACf,IAAK,gBAAgB,CACpB,MAAO,CAAAtC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEuC,cAAc,IAAKhK,SAAS,CAAGyH,WAAW,CAACuC,cAAc,CAAGxF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwF,cAAc,CAChH,IAAK,eAAe,CACnB;AACA,MAAO,CAAAvC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwC,4BAA4B,IAAKjK,SAAS,CAAGyH,WAAW,CAACwC,4BAA4B,CAAGzF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEyF,4BAA4B,CAC1J,IAAK,UAAU,CACd,MAAO,CAAAxC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEyC,QAAQ,IAAKlK,SAAS,CAAGyH,WAAW,CAACyC,QAAQ,CAAG1F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE0F,QAAQ,CAC9F,IAAK,eAAe,CACnB,MAAO,CAAAzC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEP,aAAa,IAAKlH,SAAS,CAAGyH,WAAW,CAACP,aAAa,CAAG1C,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE0C,aAAa,CAC7G,QACC,MAAO,CAAA1C,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGuF,QAAQ,CAAC,CACpC,CACD,CAAC,IAAM,CACN;AACA,GAAIA,QAAQ,GAAK,eAAe,CAAE,CACjC,MAAO,CAAAvF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEyF,4BAA4B,CACrD,CACA,MAAO,CAAAzF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGuF,QAAQ,CAAC,CACnC,CACD,CAAC,CAED,KAAM,CAAAI,kBAAkB,CAAGA,CAAClP,OAAY,CAAEuJ,eAAoB,CAAEiD,WAAgB,CAAErL,IAAS,CAAED,GAAQ,GAAK,CACzGlB,OAAO,CAACkF,KAAK,CAACF,QAAQ,CAAG,UAAU,CACnChF,OAAO,CAACkF,KAAK,CAAC/D,IAAI,CAAG,GAAGA,IAAI,IAAI,CAChCnB,OAAO,CAACkF,KAAK,CAAChE,GAAG,CAAG,GAAGA,GAAG,IAAI,CAC9BlB,OAAO,CAACkF,KAAK,CAACxD,KAAK,CAAG,GAAG6H,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE4F,IAAI,IAAI,CAAE;AACpDnP,OAAO,CAACkF,KAAK,CAACrD,MAAM,CAAG,GAAG0H,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE4F,IAAI,IAAI,CACnDnP,OAAO,CAACkF,KAAK,CAACC,eAAe,CAAGoE,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE6F,KAAK,CACtDpP,OAAO,CAACkF,KAAK,CAACoF,YAAY,CAAG,KAAK,CAClCtK,OAAO,CAACkF,KAAK,CAACmK,MAAM,CAAG,iBAAiB,CAAE;AAC1CrP,OAAO,CAACkF,KAAK,CAACoK,UAAU,CAAG,MAAM,CACjCtP,OAAO,CAACkF,KAAK,CAACqK,aAAa,CAAG,MAAM,CAAE;AACtCvP,OAAO,CAACwP,SAAS,CAAG,EAAE,CAEtB,GAAI,CAAAjG,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkG,IAAI,IAAK,MAAM,EAAI,CAAAlG,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkG,IAAI,IAAK,UAAU,CAAE,CAC7E,KAAM,CAAAC,QAAQ,CAAGtP,QAAQ,CAACuP,aAAa,CAAC,MAAM,CAAC,CAC/CD,QAAQ,CAACE,SAAS,CAAGrG,eAAe,CAACkG,IAAI,GAAK,MAAM,CAAG,GAAG,CAAG,GAAG,CAChEC,QAAQ,CAACxK,KAAK,CAAC+C,KAAK,CAAG,OAAO,CAC9ByH,QAAQ,CAACxK,KAAK,CAAC2K,QAAQ,CAAG,MAAM,CAChCH,QAAQ,CAACxK,KAAK,CAAC0C,UAAU,CAAG,MAAM,CAClC8H,QAAQ,CAACxK,KAAK,CAAC6C,SAAS,CAAGwB,eAAe,CAACkG,IAAI,GAAK,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAChFC,QAAQ,CAACxK,KAAK,CAACO,OAAO,CAAG,MAAM,CAC/BiK,QAAQ,CAACxK,KAAK,CAAC4K,UAAU,CAAG,QAAQ,CACpCJ,QAAQ,CAACxK,KAAK,CAAC6K,cAAc,CAAG,QAAQ,CACxCL,QAAQ,CAACxK,KAAK,CAACxD,KAAK,CAAG,MAAM,CAC7BgO,QAAQ,CAACxK,KAAK,CAACrD,MAAM,CAAG,MAAM,CAC9B7B,OAAO,CAACgQ,WAAW,CAACN,QAAQ,CAAC,CAC9B,CAEA;AACA;AACA,KAAM,CAAAO,qBAAqB,CAAGpB,kBAAkB,CAAC,gBAAgB,CAAEtF,eAAe,CAAEiD,WAAW,CAAC,CAChG,KAAM,CAAA0D,WAAW,CAAG9Q,oBAAoB,GAAK,SAAS,CAClD6Q,qBAAqB,GAAK,KAAK,EAAI,CAACjQ,OAAO,CAACmQ,aAAa,CACzD5G,eAAe,EAAIrK,gBAAgB,EAAI,CAACc,OAAO,CAACmQ,aAAc,CAElE,GAAID,WAAW,CAAE,CACPlQ,OAAO,CAACoQ,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC,CACxCrQ,OAAO,CAACoQ,SAAS,CAAC1K,MAAM,CAAC,yBAAyB,CAAC,CACvD,CAAC,IAAM,CACH1F,OAAO,CAACoQ,SAAS,CAAC1K,MAAM,CAAC,iBAAiB,CAAC,CAC3C1F,OAAO,CAACoQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACpD,CAEN;AACArQ,OAAO,CAACkF,KAAK,CAACO,OAAO,CAAG,MAAM,CAC9BzF,OAAO,CAACkF,KAAK,CAACqK,aAAa,CAAG,MAAM,CAEpC;AACA;AACA;AACA,KAAM,CAAAe,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEtF,eAAe,CAAEiD,WAAW,CAAC,CACvF,GAAI8D,aAAa,CAAE,CAClBtR,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACA;AAAA,CAGD;AACA;AACA,GAAI,CAACgB,OAAO,CAACuQ,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,KAAM,CAAAC,UAAU,CAAGxQ,OAAO,CAACyQ,SAAS,CAAC,IAAI,CAAgB,CACzD;AACA,GAAIzQ,OAAO,CAACmQ,aAAa,GAAKpL,SAAS,CAAE,CAC9ByL,UAAU,CAASL,aAAa,CAAGnQ,OAAO,CAACmQ,aAAa,CAC7D,CACN,GAAInQ,OAAO,CAAC0Q,UAAU,CAAE,CACvB1Q,OAAO,CAAC0Q,UAAU,CAACC,YAAY,CAACH,UAAU,CAAExQ,OAAO,CAAC,CACpDA,OAAO,CAAGwQ,UAAU,CACrB,CACD,CAEA;AACAxQ,OAAO,CAACkF,KAAK,CAACqK,aAAa,CAAG,MAAM,CAEpC;AACA,KAAM,CAAAqB,QAAQ,CAAG/B,kBAAkB,CAAC,UAAU,CAAEtF,eAAe,CAAEiD,WAAW,CAAC,CAC7E,KAAM,CAAAqE,WAAW,CAAI7D,CAAQ,EAAK,CACjCA,CAAC,CAAC8D,eAAe,CAAC,CAAC,CACnBvM,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIoM,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACA5R,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACA,KAAM,CAAA0S,oBAAoB,CAAGlC,kBAAkB,CAAC,eAAe,CAAEtF,eAAe,CAAEiD,WAAW,CAAC,CAC9F,GAAIuE,oBAAoB,CAAE,CACzB/Q,OAAO,CAACoQ,SAAS,CAAC1K,MAAM,CAAC,iBAAiB,CAAC,CAC3C1F,OAAO,CAACoQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChDrQ,OAAO,CAACmQ,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED,KAAM,CAAAa,cAAc,CAAIhE,CAAQ,EAAK,CACpCA,CAAC,CAAC8D,eAAe,CAAC,CAAC,CAEnB;AACA;AACA,KAAM,CAAAR,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEtF,eAAe,CAAEiD,WAAW,CAAC,CACvF,GAAIoE,QAAQ,GAAK,kBAAkB,EAAI,CAACN,aAAa,CAAE,CACtD;AAAA,CAEF,CAAC,CAED,KAAM,CAAAW,WAAW,CAAIjE,CAAQ,EAAK,CACjCA,CAAC,CAAC8D,eAAe,CAAC,CAAC,CACnBvM,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIoM,QAAQ,GAAK,kBAAkB,EAAI,CAACA,QAAQ,CAAE,CACjD;AACA5R,cAAc,CAAC,CAACC,WAAW,CAAC,CAE5B;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACJ,KAAM,CAAAyS,oBAAoB,CAAGlC,kBAAkB,CAAC,eAAe,CAAEtF,eAAe,CAAEiD,WAAW,CAAC,CAC1F,GAAIuE,oBAAoB,CAAE,CACzB/Q,OAAO,CAACoQ,SAAS,CAAC1K,MAAM,CAAC,iBAAiB,CAAC,CAC3C1F,OAAO,CAACoQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChDrQ,OAAO,CAACmQ,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED;AACA,GAAI,CAACnQ,OAAO,CAACuQ,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,GAAIK,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACA5Q,OAAO,CAACgK,gBAAgB,CAAC,WAAW,CAAE6G,WAAW,CAAC,CAClD7Q,OAAO,CAACgK,gBAAgB,CAAC,UAAU,CAAEgH,cAAc,CAAC,CAEpD;AACAhR,OAAO,CAACgK,gBAAgB,CAAC,OAAO,CAAEiH,WAAW,CAAC,CAC/C,CAAC,IAAM,CACN;AACAjR,OAAO,CAACgK,gBAAgB,CAAC,OAAO,CAAEiH,WAAW,CAAC,CAC/C,CAEA;AACAjR,OAAO,CAACkR,YAAY,CAAC,yBAAyB,CAAE,MAAM,CAAC,CACxD,CACD,CAAC,CACDrW,SAAS,CAAC,IAAM,CACf,GAAI,CAAAkG,OAAO,CACX,GAAI,CAAAoQ,KAAK,CAET,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,KAAAC,uBAAA,CAAAC,uBAAA,CAAAC,MAAA,CAAAC,OAAA,CACH;AACAL,KAAK,CAAG,CAAAhT,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEyC,SAAS,GAAI,EAAE,CAEvC;AACA,KAAM,CAAA6Q,WAAW,CAAGrS,oBAAoB,GAAK,SAAS,EAAIjB,cAAc,SAAdA,cAAc,YAAAkT,uBAAA,CAAdlT,cAAc,CAAEyC,SAAS,UAAAyQ,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4B/T,WAAW,CAAG,CAAC,CAAC,UAAAgU,uBAAA,WAA5CA,uBAAA,CAA8CzQ,WAAW,CAC/G1C,cAAc,CAACyC,SAAS,CAACtD,WAAW,CAAG,CAAC,CAAC,CAASuD,WAAW,CAC9D,EAAA0Q,MAAA,CAAAJ,KAAK,UAAAI,MAAA,kBAAAC,OAAA,CAALD,MAAA,CAAQ,CAAC,CAAC,UAAAC,OAAA,iBAAVA,OAAA,CAAY3Q,WAAW,GAAI,EAAE,CAEhCE,OAAO,CAAGd,iBAAiB,CAACwR,WAAW,EAAI,EAAE,CAAC,CAC9ChS,gBAAgB,CAACsB,OAAO,CAAC,CAEzB,GAAIA,OAAO,CAAE,CACZ;AAAA,CAGD;AACA,KAAM,CAAA2Q,iBAAiB,CAAGhT,gBAAgB,GAAK,SAAS,EACvDU,oBAAoB,GAAK,SAAS,EAClCrC,KAAK,GAAK,SAAS,EAClB2B,gBAAgB,GAAK,MAAM,EAAIU,oBAAoB,GAAK,SAAU,CAEpE,GAAIsS,iBAAiB,CAAE,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAItB;AACA,GAAI,CAAAzI,eAAe,CACnB,GAAI,CAAAiD,WAAW,CAEf,GAAIpN,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAgT,sBAAA,CAApBhT,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAAqU,sBAAA,WAAvCA,sBAAA,CAAyCnI,QAAQ,CAAE,KAAAyI,uBAAA,CAAAC,uBAAA,CAC5F;AACA3I,eAAe,CAAG5K,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACkM,QAAQ,CAChEgD,WAAW,CAAGrO,cAAc,SAAdA,cAAc,kBAAA8T,uBAAA,CAAd9T,cAAc,CAAEyC,SAAS,UAAAqR,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B3U,WAAW,CAAG,CAAC,CAAC,UAAA4U,uBAAA,iBAA5CA,uBAAA,CAA8C9K,OAAO,CACpE,CAAC,IAAM,IAAIzI,oBAAoB,SAApBA,oBAAoB,YAAAiT,sBAAA,CAApBjT,oBAAoB,CAAG,CAAC,CAAC,UAAAiT,sBAAA,WAAzBA,sBAAA,CAA2BpI,QAAQ,CAAE,KAAA2I,uBAAA,CAAAC,uBAAA,CAC/C;AACA7I,eAAe,CAAG5K,oBAAoB,CAAC,CAAC,CAAC,CAAC6K,QAAQ,CAClDgD,WAAW,CAAGrO,cAAc,SAAdA,cAAc,kBAAAgU,uBAAA,CAAdhU,cAAc,CAAEyC,SAAS,UAAAuR,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgChL,OAAO,CACtD,CAAC,IAAM,KAAAiL,uBAAA,CAAAC,uBAAA,CACN;AACA/I,eAAe,CAAG,CACjBG,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACd8F,IAAI,CAAE,UAAU,CAChBL,KAAK,CAAE,QAAQ,CACfD,IAAI,CAAE,IAAI,CACVJ,cAAc,CAAE,IAAI,CACpBC,4BAA4B,CAAE,IAAI,CAClCC,QAAQ,CAAE,kBAAkB,CAC5BhD,aAAa,CAAE,KAChB,CAAC,CACDO,WAAW,CAAG,CAAArO,cAAc,SAAdA,cAAc,kBAAAkU,uBAAA,CAAdlU,cAAc,CAAEyC,SAAS,UAAAyR,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B/U,WAAW,CAAG,CAAC,CAAC,UAAAgV,uBAAA,iBAA5CA,uBAAA,CAA8ClL,OAAO,GAAI,CAAC,CAAC,CAC1E,CACA,KAAM,CAAA7E,OAAO,CAAGkH,UAAU,CAAC,EAAAoI,gBAAA,CAAAtI,eAAe,UAAAsI,gBAAA,iBAAfA,gBAAA,CAAiBnI,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAlH,OAAO,CAAGiH,UAAU,CAAC,EAAAqI,iBAAA,CAAAvI,eAAe,UAAAuI,iBAAA,iBAAfA,iBAAA,CAAiBnI,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAA4I,kBAAkB,CAAG9I,UAAU,CAAC,EAAAsI,iBAAA,CAAAxI,eAAe,UAAAwI,iBAAA,iBAAfA,iBAAA,CAAiB5C,IAAI,GAAI,IAAI,CAAC,CAEpE;AACAtP,cAAc,CAAC0S,kBAAkB,CAAC,CAElC,GAAI,CAAApR,IAAI,CAAED,GAAG,CACb,GAAIH,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CE,IAAI,CAAGH,IAAI,CAAC0B,CAAC,CAAGH,OAAO,CACvBrB,GAAG,CAAGF,IAAI,CAAC4B,CAAC,EAAIJ,OAAO,CAAG,CAAC,CAAG,CAACA,OAAO,CAAGb,IAAI,CAAC6Q,GAAG,CAAChQ,OAAO,CAAC,CAAC,CAE3D;AACA,KAAM,CAAAoH,QAAQ,CAAGvH,sBAAsB,CAACrB,IAAI,CAAEuR,kBAAkB,CAAEhQ,OAAO,CAAEC,OAAO,CAAC,CACnF7C,gBAAgB,CAACiK,QAAQ,CAAC,CAC3B,CAEA;AACA,KAAM,CAAArE,eAAe,CAAGnF,QAAQ,CAACoF,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBvF,OAAO,CAAGuF,eAAe,CACzB;AACD,CAAC,IAAM,CACN;AACAvF,OAAO,CAAGI,QAAQ,CAACuP,aAAa,CAAC,KAAK,CAAC,CACvC3P,OAAO,CAACyS,EAAE,CAAG,cAAc,CAAE;AAC7BzS,OAAO,CAACmQ,aAAa,CAAG,KAAK,CAAE;AAC/B/P,QAAQ,CAACsS,IAAI,CAAC1C,WAAW,CAAChQ,OAAO,CAAC,CACnC,CAEAA,OAAO,CAACkF,KAAK,CAACyN,MAAM,CAAG,SAAS,CAChC3S,OAAO,CAACkF,KAAK,CAACqK,aAAa,CAAG,MAAM,CAAE;AAEtC;AACAvP,OAAO,CAACkF,KAAK,CAACmK,MAAM,CAAG,MAAM,CAE7B;AACA,IAAA2C,iBAAA,CAAIzI,eAAe,UAAAyI,iBAAA,WAAfA,iBAAA,CAAiB/F,aAAa,CAAE,CACnCjN,cAAc,CAAC,IAAI,CAAC,CACrB,CAEA;AACAkQ,kBAAkB,CAAClP,OAAO,CAAEuJ,eAAe,CAAEiD,WAAW,CAAErL,IAAI,CAAED,GAAG,CAAC,CAEpE;AACA,KAAM,CAAAoP,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEtF,eAAe,CAAEiD,WAAW,CAAC,CACvF,GAAI8D,aAAa,CAAE,CAClBtR,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AAAA,CAGD;AACD,CACD,CAAE,MAAO4T,KAAK,CAAE,CACfrO,OAAO,CAACqO,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACpD,CACD,CAAC,CAEDxB,iBAAiB,CAAC,CAAC,CAEnB,MAAO,IAAM,CACZ,KAAM,CAAA7L,eAAe,CAAGnF,QAAQ,CAACoF,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAACsN,OAAO,CAAG,IAAI,CAC9BtN,eAAe,CAACuN,WAAW,CAAG,IAAI,CAClCvN,eAAe,CAACwN,UAAU,CAAG,IAAI,CAClC,CACD,CAAC,CACF,CAAC,CAAE,CACF5U,cAAc,CACdQ,oBAAoB,CACpBJ,kBAAkB,CAClBC,kBAAkB,CAClBY,oBAAoB,CACpB9B,WACA;AAAA,CACA,CAAC,CACF,KAAM,CAAA0V,cAAc,CAAG,CAAA7U,cAAc,SAAdA,cAAc,kBAAAzB,uBAAA,CAAdyB,cAAc,CAAEyC,SAAS,UAAAlE,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgCsW,OAAO,UAAArW,uBAAA,iBAAvCA,uBAAA,CAAyCsW,cAAc,GAAI,KAAK,CAEvF,QAAS,CAAAC,mBAAmBA,CAAC9T,cAAmB,CAAE,KAAA+T,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACjD,GAAIjU,cAAc,GAAK,CAAC,CAAE,CACzB,MAAO,MAAM,CACd,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,QAAQ,CAChB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAEA,MAAO,CAAAlB,cAAc,SAAdA,cAAc,kBAAAiV,uBAAA,CAAdjV,cAAc,CAAEyC,SAAS,UAAAwS,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgCJ,OAAO,UAAAK,uBAAA,iBAAvCA,uBAAA,CAAyCC,gBAAgB,GAAI,MAAM,CAC3E,CACA,KAAM,CAAAC,gBAAgB,CAAGL,mBAAmB,CAAC9T,cAAc,CAAC,CAC5D,KAAM,CAAAoU,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACT,cAAc,CAAE,MAAO,KAAI,CAEhC,GAAIQ,gBAAgB,GAAK,MAAM,CAAE,CAChC,mBACC7X,IAAA,CAACP,aAAa,EACbsY,OAAO,CAAC,MAAM,CACdvC,KAAK,CAAE5T,UAAW,CAClByH,QAAQ,CAAC,QAAQ,CACjB2O,UAAU,CAAErW,WAAW,CAAG,CAAE,CAC5BsW,EAAE,CAAE,CACHzO,eAAe,CAAE,aAAa,CAC9BH,QAAQ,CAAE,oBAAoB,CAC9B,+BAA+B,CAAE,CAChCG,eAAe,CAAE7F,aAAe;AACjC,CACD,CAAE,CACFuU,UAAU,cAAElY,IAAA,CAACV,MAAM,EAACiK,KAAK,CAAE,CAAE4O,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxDC,UAAU,cAAEpY,IAAA,CAACV,MAAM,EAACiK,KAAK,CAAE,CAAE4O,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxD,CAAC,CAEJ,CACA,GAAIN,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACC7X,IAAA,CAACX,GAAG,EAAC4Y,EAAE,CAAE,CAAEnO,OAAO,CAAE,MAAM,CAAEqK,UAAU,CAAE,QAAQ,CAAEkE,YAAY,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAK,CAAEC,OAAO,CAAE,KAAM,CAAE,CAAAC,QAAA,CAGrGC,KAAK,CAACC,IAAI,CAAC,CAAEzF,MAAM,CAAErR,UAAW,CAAC,CAAC,CAACmJ,GAAG,CAAC,CAAC4N,CAAC,CAAEC,KAAK,gBAChD5Y,IAAA,QAECuJ,KAAK,CAAE,CACNxD,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,KAAK,CACbsD,eAAe,CAAEoP,KAAK,GAAKjX,WAAW,CAAG,CAAC,CAAGgC,aAAa,CAAG,SAAS,CAAE;AACxEgL,YAAY,CAAE,OACf,CAAE,EANGiK,KAOL,CACD,CAAC,CACE,CAAC,CAER,CACA,GAAIf,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACC7X,IAAA,CAACX,GAAG,EAAC4Y,EAAE,CAAE,CAAEnO,OAAO,CAAE,MAAM,CAAEqK,UAAU,CAAE,QAAQ,CAAEkE,YAAY,CAAE,YAAa,CAAE,CAAAG,QAAA,cAC9EtY,KAAA,CAACP,UAAU,EAACsY,EAAE,CAAE,CAAEM,OAAO,CAAE,KAAK,CAAEjM,KAAK,CAAE3I,aAAc,CAAE,CAAA6U,QAAA,EAAC,OACpD,CAAC7W,WAAW,CAAC,MAAI,CAACC,UAAU,EACtB,CAAC,CACT,CAAC,CAER,CAEA,GAAIiW,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACC7X,IAAA,CAACX,GAAG,EAAAmZ,QAAA,cACHxY,IAAA,CAACL,UAAU,EAACoY,OAAO,CAAC,OAAO,CAAAS,QAAA,cAC1BxY,IAAA,CAACR,cAAc,EACduY,OAAO,CAAC,aAAa,CACrBc,KAAK,CAAE/W,QAAS,CAChBmW,EAAE,CAAE,CACH/R,MAAM,CAAE,KAAK,CACXyI,YAAY,CAAE,MAAM,CACpBmK,MAAM,CAAE,UAAU,CACpB,0BAA0B,CAAE,CAC3BtP,eAAe,CAAE7F,aAAe;AACjC,CACD,CAAE,CACF,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACZ,CAAC,CACD,mBACCzD,KAAA,CAAAE,SAAA,EAAAoY,QAAA,EACE3U,aAAa,eACb7D,IAAA,QAAAwY,QAAA,CAcElV,WAAW,eACXpD,KAAA,CAACR,OAAO,EACPoQ,IAAI,CAAEiJ,OAAO,CAAChV,aAAa,CAAC,EAAIgV,OAAO,CAAC7X,QAAQ,CAAE,CAClDA,QAAQ,CAAEA,QAAS,CACnBK,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACFoK,YAAY,CAAEA,YAAa,CAC3BG,eAAe,CAAEA,eAAgB,CACjCkN,eAAe,CAAC,gBAAgB,CAChCC,cAAc,CACblV,aAAa,CACV,CACAwB,GAAG,CAAExB,aAAa,CAACwB,GAAG,EAAIuI,UAAU,CAAC1K,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAAG,CAAC0K,UAAU,CAAC1K,YAAY,EAAI,GAAG,CAAC,CAAG4C,IAAI,CAAC6Q,GAAG,CAAC/I,UAAU,CAAC1K,YAAY,EAAI,GAAG,CAAC,CAAC,CAAC,CAC7IoC,IAAI,CAAEzB,aAAa,CAACyB,IAAI,CAAGsI,UAAU,CAAC3K,YAAY,EAAI,GAAG,CACzD,CAAC,CACDiG,SACH,CACD6O,EAAE,CAAE,CACH;AACA;AACA;AACA,gBAAgB,CAAE/W,QAAQ,CAAG,MAAM,CAAG,MAAM,CAC5C,8CAA8C,CAAE,CAC/CwS,MAAM,CAAE,IAAI,CACZ;AACA,GAAGhF,WAAW,CACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAGqE,iBAAiB,CAAC,CAAA5Q,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE4J,QAAQ,GAAI,eAAe,CAAC,CACnExG,GAAG,CAAE,GAAG,CAAC,CAAAxB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEwB,GAAG,GAAI,CAAC,GAC5BnC,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC3C0K,UAAU,CAAC1K,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAClC,CAAC0K,UAAU,CAAC1K,YAAY,EAAI,GAAG,CAAC,CAChC4C,IAAI,CAAC6Q,GAAG,CAAC/I,UAAU,CAAC1K,YAAY,EAAI,GAAG,CAAC,CAAC,CAC1C,CAAC,CAAC,eAAe,CACrBoC,IAAI,CAAE,GAAG,CAAC,CAAAzB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEyB,IAAI,GAAI,CAAC,GAAKrC,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC9E2K,UAAU,CAAC3K,YAAY,CAAC,EAAI,CAAC,CAC9B,CAAC,CAAE,eAAe,CACrB+V,QAAQ,CAAE,QAAQ,CAClB;AACAvF,UAAU,CAAE,uCACb,CACD,CAAE,CACFwF,iBAAiB,CAAE,IAAK,CAAAX,QAAA,eAExBxY,IAAA,QAAKuJ,KAAK,CAAE,CAAE8O,YAAY,CAAE,KAAK,CAAEvO,OAAO,CAAE,MAAO,CAAE,CAAA0O,QAAA,CACnD,CAAAtW,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkX,aAAa,gBAC9BpZ,IAAA,CAACT,UAAU,EACV8Z,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACFpB,EAAE,CAAE,CACH5O,QAAQ,CAAE,OAAO,CACjBiQ,SAAS,CAAE,iCAAiC,CAC5C9T,IAAI,CAAE,MAAM,CACZ6C,KAAK,CAAE,MAAM,CACbyQ,MAAM,CAAE,OAAO,CACfS,UAAU,CAAE,iBAAiB,CAC7BC,MAAM,CAAE,gBAAgB,CACxB9F,MAAM,CAAE,QAAQ,CAChB/E,YAAY,CAAE,MAAM,CACpB4J,OAAO,CAAE,gBACV,CAAE,CAAAC,QAAA,cAEFxY,IAAA,CAACJ,SAAS,EAACqY,EAAE,CAAE,CAAEwB,IAAI,CAAE,CAAC,CAAEnN,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAClC,CACZ,CACG,CAAC,cACNtM,IAAA,CAACF,gBAAgB,EAEpB4Z,GAAG,CAAE/T,YAAa,CAClB4D,KAAK,CAAE,CAAEoQ,SAAS,CAAE,OAAQ,CAAE,CAC9BC,OAAO,CAAE,CACRC,eAAe,CAAE,CAACpU,cAAc,CAChCqU,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAA3B,QAAA,cAECxY,IAAA,QAAKuJ,KAAK,CAAE,CACXoQ,SAAS,CAAE,OAAO,CAClBT,QAAQ,CAAE,aACX,CAAE,CAAAV,QAAA,cACDtY,KAAA,CAACb,GAAG,EAACkK,KAAK,CAAE,CACXgP,OAAO,CAAE,CAAApW,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEiY,OAAO,GAAI,MAAM,CAC5ClU,MAAM,CAAEiJ,aACT,CAAE,CAAAqJ,QAAA,eACDtY,KAAA,CAACb,GAAG,EACHqa,GAAG,CAAEvV,UAAW,CAChB2F,OAAO,CAAC,MAAM,CACduQ,aAAa,CAAC,QAAQ,CACtBC,QAAQ,CAAC,MAAM,CACflG,cAAc,CAAC,QAAQ,CACvB6D,EAAE,CAAE,CACHlS,KAAK,CAAE,MAAM,CACbwS,OAAO,CAAE,KAAK,CACdgC,SAAS,CAAE,YACZ,CAAE,CAAA/B,QAAA,EAEDxW,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE+I,GAAG,CAAEyP,SAAc,EACpCA,SAAS,CAACpL,WAAW,CAACrE,GAAG,CAAC,CAAC0P,SAAc,CAAEC,QAAgB,gBAC1D1a,IAAA,CAACX,GAAG,EAEHsb,SAAS,CAAC,KAAK,CACfC,GAAG,CAAEH,SAAS,CAACI,GAAI,CACnBC,GAAG,CAAEL,SAAS,CAACM,OAAO,EAAI,OAAQ,CAClC9C,EAAE,CAAE,CACH0B,SAAS,CAAEa,SAAS,CAACQ,cAAc,EAAIP,SAAS,CAACO,cAAc,EAAI,OAAO,CAC1ExO,SAAS,CAAEgO,SAAS,CAAC/N,SAAS,EAAI,QAAQ,CAC1CwO,SAAS,CAAER,SAAS,CAACS,GAAG,EAAI,SAAS,CACrC;AACAhV,MAAM,CAAE,GAAGuU,SAAS,CAACpL,aAAa,EAAI,GAAG,IAAI,CAC7CkK,UAAU,CAAEkB,SAAS,CAACvL,eAAe,EAAI,SAAS,CAClD4J,MAAM,CAAE,QACT,CAAE,CACFO,OAAO,CAAEA,CAAA,GAAM,CACd,GAAImB,SAAS,CAACW,SAAS,CAAE,CACxB,KAAM,CAAA1L,SAAS,CAAG+K,SAAS,CAACW,SAAS,CACrChU,MAAM,CAAC2I,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAE,CACFlG,KAAK,CAAE,CAAEyN,MAAM,CAAEwD,SAAS,CAACW,SAAS,CAAG,SAAS,CAAG,SAAU,CAAE,EAnB1D,GAAGX,SAAS,CAACpP,EAAE,IAAIsP,QAAQ,EAoBhC,CACD,CACF,CAAC,CAEA3Y,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEgJ,GAAG,CACxB,CAACqQ,SAAc,CAAExC,KAAU,QAAAyC,qBAAA,CAAAC,sBAAA,OAC1B,CAAAF,SAAS,CAACG,IAAI,eACbvb,IAAA,CAACL,UAAU,EACV6b,SAAS,CAAC,eAAe,CACG;AAC5BvD,EAAE,CAAE,CACHzL,SAAS,CAAE,EAAA6O,qBAAA,CAAAD,SAAS,CAAClP,cAAc,UAAAmP,qBAAA,iBAAxBA,qBAAA,CAA0BI,UAAU,GAAIzP,SAAS,CAACQ,SAAS,CACtEF,KAAK,CAAE,EAAAgP,sBAAA,CAAAF,SAAS,CAAClP,cAAc,UAAAoP,sBAAA,iBAAxBA,sBAAA,CAA0B/O,SAAS,GAAIP,SAAS,CAACM,KAAK,CAC7DoP,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvBpD,OAAO,CAAE,OAAO,CAChBqD,QAAQ,CAAE,YAAY,CACtBC,YAAY,CAAE,YAAY,CAC1BC,OAAO,CAAE,MACV,CAAE,CACFC,uBAAuB,CAAErP,iBAAiB,CAAC0O,SAAS,CAACG,IAAI,CAAG;AAAA,EAXvDH,SAAS,CAAChQ,EAAE,EAAIwN,KAYrB,CACD,EACH,CAAC,EACG,CAAC,CAELoD,MAAM,CAACC,IAAI,CAAC1N,cAAc,CAAC,CAACxD,GAAG,CAAEyD,WAAW,OAAA0N,qBAAA,CAAAC,sBAAA,oBAC5Cnc,IAAA,CAACX,GAAG,EACHqa,GAAG,CAAEtV,kBAAmB,CAExB6T,EAAE,CAAE,CACHnO,OAAO,CAAE,MAAM,CACfsK,cAAc,CAAEvB,YAAY,EAAAqJ,qBAAA,CAAC3N,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAA0N,qBAAA,iBAA9BA,qBAAA,CAAgCzP,SAAS,CAAC,CACvE6N,QAAQ,CAAE,MAAM,CAChBxB,MAAM,CAAE,OAAO,CACftP,eAAe,EAAA2S,sBAAA,CAAE5N,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAA2N,sBAAA,iBAA9BA,sBAAA,CAAgCjN,eAAe,CAChEqJ,OAAO,CAAE,OAAO,CAChBxS,KAAK,CAAE,MACR,CAAE,CAAAyS,QAAA,CAEDjK,cAAc,CAACC,WAAW,CAAC,CAACzD,GAAG,CAAC,CAACG,MAAW,CAAE0N,KAAa,QAAAwD,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC3D1c,IAAA,CAACV,MAAM,EAEN+Z,OAAO,CAAEA,CAAA,GAAM/J,kBAAkB,CAACpE,MAAM,CAACyR,YAAY,CAAE,CACvD5E,OAAO,CAAC,WAAW,CACnBE,EAAE,CAAE,CACHa,MAAM,CAAE,eAAe,CACvBtP,eAAe,CAAE,EAAA4S,qBAAA,CAAAlR,MAAM,CAAC0R,gBAAgB,UAAAR,qBAAA,iBAAvBA,qBAAA,CAAyBS,qBAAqB,GAAI,SAAS,CAC5EvQ,KAAK,CAAE,EAAA+P,sBAAA,CAAAnR,MAAM,CAAC0R,gBAAgB,UAAAP,sBAAA,iBAAvBA,sBAAA,CAAyBS,eAAe,GAAI,MAAM,CACzDtD,MAAM,CAAE,EAAA8C,sBAAA,CAAApR,MAAM,CAAC0R,gBAAgB,UAAAN,sBAAA,iBAAvBA,sBAAA,CAAyBS,iBAAiB,GAAI,aAAa,CACnE7I,QAAQ,CAAE,EAAAqI,sBAAA,CAAArR,MAAM,CAAC0R,gBAAgB,UAAAL,sBAAA,iBAAvBA,sBAAA,CAAyBS,QAAQ,GAAI,MAAM,CACrDjX,KAAK,CAAE,EAAAyW,sBAAA,CAAAtR,MAAM,CAAC0R,gBAAgB,UAAAJ,sBAAA,iBAAvBA,sBAAA,CAAyBlP,KAAK,GAAI,MAAM,CAC/CiL,OAAO,CAAE,SAAS,CAClB0E,UAAU,CAAE,QAAQ,CACpBC,aAAa,CAAE,MAAM,CACrBvO,YAAY,CAAE,EAAA8N,sBAAA,CAAAvR,MAAM,CAAC0R,gBAAgB,UAAAH,sBAAA,iBAAvBA,sBAAA,CAAyBU,YAAY,GAAI,KAAK,CAC5D7D,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACV9P,eAAe,CAAE,EAAAkT,sBAAA,CAAAxR,MAAM,CAAC0R,gBAAgB,UAAAF,sBAAA,iBAAvBA,sBAAA,CAAyBG,qBAAqB,GAAI,SAAS,CAAE;AAC9EO,OAAO,CAAE,GAAG,CAAE;AACd9D,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CAAAd,QAAA,CAEDtN,MAAM,CAACmS,UAAU,EAtBbzE,KAuBE,CAAC,EACT,CAAC,EArCGpK,WAsCD,CAAC,EACN,CAAC,EACE,CAAC,CAGD,CAAC,EAhIL,aAAa/I,cAAc,EAiIV,CAAC,CAElB4R,cAAc,EAAIzV,UAAU,CAAC,CAAC,EAAImB,gBAAgB,GAAK,MAAM,eAAI/C,IAAA,CAACX,GAAG,EAAAmZ,QAAA,CAAEV,cAAc,CAAC,CAAC,CAAM,CAAC,CAAE,GAAG,EAC7F,CACT,CAEG,CACL,cAED9X,IAAA,UAAAwY,QAAA,CACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CACC,CAAC,EAEP,CAAC,CAEL,CAAC,CAED,cAAe,CAAAnY,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}